@echo off
setlocal enabledelayedexpansion

echo 🧪 Testing Workflow Integration Service Startup...
echo ==================================================

echo 🚀 Starting Workflow Integration Service...
cd workflow-integration

REM Check if Maven is available
mvn -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Maven is not installed or not in PATH
    exit /b 1
)

echo 📦 Building and starting the application...
echo 📋 Logs will be written to startup.log

REM Start the application in background
start /b mvn spring-boot:run > ..\startup.log 2>&1

echo 🔄 Application started
echo ⏳ Waiting for application to initialize...

REM Wait for application to start
timeout /t 10 /nobreak >nul

REM Test health endpoint
echo 🔍 Testing health endpoint...
curl -s -f "http://localhost:8082/actuator/health" >nul 2>&1
if errorlevel 1 (
    echo ❌ Health check failed
    echo 📋 Last lines of startup.log:
    powershell -command "Get-Content ..\startup.log | Select-Object -Last 20"
    exit /b 1
) else (
    echo ✅ Health check passed
)

REM Test API endpoints
echo 🔍 Testing API endpoints...

curl -s -f "http://localhost:8082/api/v1/workflow/bpmn/definitions" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Process Definitions API not ready
) else (
    echo ✅ Process Definitions API working
)

curl -s -f "http://localhost:8082/api/v1/workflow/bpmn/unified/definitions" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Unified Process Definitions API not ready
) else (
    echo ✅ Unified Process Definitions API working
)

echo.
echo ✅ Startup test completed!
echo 📋 Full logs are available in startup.log
echo.
echo 🎯 Next steps:
echo    1. Start the full Docker Compose stack: docker-compose up -d
echo    2. Run integration tests: npm test
echo    3. Access BPMN Viewer: http://localhost:3001
echo.
echo 🛑 Press any key to stop the application...
pause >nul

REM Stop the application
echo Stopping application...
taskkill /f /im java.exe >nul 2>&1

cd ..

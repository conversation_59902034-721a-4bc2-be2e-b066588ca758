# BPMN Workflow System

A complete BPMN workflow management system with clean architecture:

## 🏗️ Architecture

### Spring Boot Backend
- **Serves Flowable engine & custom REST API**
- **Starts process instances**
- **Lists running instances** 
- **Serves BPMN XML for bpmn-js**

### React Frontend
- **Lists available processes**
- **Renders BPMN diagram via bpmn-js**
- **Starts process instances**
- **Lists running process instances**

### PostgreSQL
- **Dockerized for Flowable engine persistence**

### Docker Compose
- **`docker-compose up --build` to run all services**

## 🚀 How to Run

```bash
# In the extracted project folder
docker-compose up --build
```

**Frontend**: http://localhost:3000  
**Backend API**: http://localhost:8080/api

## 📋 API Endpoints

### Process Definitions
- `GET /api/definitions` - List all available processes
- `GET /api/definition/{id}/xml` - Get BPMN XML for rendering

### Process Instances  
- `GET /api/instances` - List active process instances
- `GET /api/instances/history` - List completed instances
- `GET /api/instance/{id}/status` - Get instance status with active tasks

### Process Management
- `POST /api/test/start-process/{processKey}` - Start test process
- `GET /api/test/available-processes` - List available process keys

## 🎯 Features

### ✅ Core Functionality
- **Process Definition Management**: View and manage BPMN processes
- **Instance Lifecycle**: Start, monitor, and track process instances
- **BPMN Visualization**: Interactive diagrams with bpmn-js
- **Active Task Highlighting**: Visual indication of current workflow state
- **Real-time Updates**: Live process status and instance data

### 🎨 BPMN Viewer
- Interactive BPMN diagram rendering
- Active task highlighting with animations
- Process instance selection and monitoring
- Export capabilities (SVG, BPMN XML)

### 🔧 Development Features
- Hot reload for frontend development
- Comprehensive logging and debugging
- Test endpoints for easy process creation
- Docker-based development environment

## 📁 Project Structure

```
├── workflow-integration/          # Spring Boot Backend
│   ├── src/main/resources/processes/  # BPMN files
│   └── Dockerfile
├── workflow-bpmn-viewer/         # React Frontend  
│   ├── src/
│   └── Dockerfile
├── docker-compose.yml            # Service orchestration
└── README.md
```

## 🧪 Testing

### Quick Test Commands
```bash
# Test API connectivity
curl http://localhost:8080/api/definitions

# Start a test process
curl -X POST http://localhost:8080/api/test/start-process/Hrdc_workflow

# Check active instances
curl http://localhost:8080/api/instances
```

### Frontend Testing
1. Open http://localhost:3000
2. View available process definitions
3. Click "View BPMN" to see diagrams
4. Use test buttons to start processes
5. Monitor active instances with highlighting

## 🔧 Development

### Backend Development
```bash
cd workflow-integration
mvn spring-boot:run
```

### Frontend Development  
```bash
cd workflow-bpmn-viewer
npm install
npm run dev
```

## 📊 Available BPMN Processes

- **HRDC Workflow** (`check-demo.bpmn20.xml`)
- **Complaint Workflow** (`check-demo1.bpmn20.xml`)
- **Appeal Workflow** (`Appeal_Workflow.bpmn20.xml`)

## 🛑 Stopping the System

```bash
docker-compose down
```

## 🔍 Troubleshooting

### Common Issues
1. **Port conflicts**: Ensure ports 3000, 8080, 5432 are available
2. **Docker memory**: Increase Docker memory if services fail to start
3. **Database connection**: Check PostgreSQL container health

### Logs
```bash
# View all logs
docker-compose logs -f

# Specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

---

## 🎯 Next Steps

Want to extend the system? Consider adding:

- **Task claiming/completion**
- **User authentication** 
- **Multi-diagram support**

The architecture is designed to be easily extensible for these features!

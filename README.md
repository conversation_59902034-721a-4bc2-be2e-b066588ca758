# BPMN Workflow Integration System

A comprehensive workflow management system that integrates Flowable BPMN engine with a React-based frontend viewer, featuring real-time active task highlighting and workflow process visualization.

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   BPMN Viewer   │    │   Workflow       │    │   Flowable      │
│   Frontend      │◄──►│   Integration    │◄──►│   REST API      │
│   (Port 3001)   │    │   (Port 8082)    │    │   (Port 8080)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         └────────────────────────┼────────────────────────┘
                                  │
                         ┌─────────────────┐
                         │   PostgreSQL    │
                         │   Database      │
                         │   (Port 5432)   │
                         └─────────────────┘
```

## 🚀 Features

### BPMN Visualization
- Interactive BPMN diagram viewer using bpmn-js
- **Real-time highlighting of active process tasks**
- Zoom, pan, and fit-to-viewport controls
- Export diagrams as SVG or download BPMN XML

### Workflow Management
- View all process definitions from Flowable REST API
- Monitor active and historic process instances
- **Active task highlighting in BPMN diagrams**
- Process instance lifecycle management

### Integration
- Dockerized Flowable REST API on port 8080
- Unified API endpoints for seamless frontend integration
- PostgreSQL database shared between services
- Real-time workflow status updates

## 📋 Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for local development)
- Java 17+ (for local development)
- Git

## 🛠️ Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd <project-directory>
```

### 2. Start All Services with Docker Compose

```bash
# Start all services (database, Flowable REST API, workflow integration, BPMN viewer)
docker-compose up -d

# View logs
docker-compose logs -f
```

### 3. Access the Applications

- **BPMN Viewer Frontend**: http://localhost:3001
- **Workflow Integration API**: http://localhost:8082
- **Flowable REST API**: http://localhost:8080/flowable-rest
- **PostgreSQL Database**: localhost:5432

### 4. Initial Setup

Wait for all services to start (approximately 2-3 minutes for first run):

```bash
# Check service health
docker-compose ps

# Verify Flowable REST API is ready
curl http://localhost:8080/flowable-rest/service/management/engine
```

## 🔧 Configuration

### Environment Variables

The system uses the following key environment variables:

```yaml
# Database Configuration
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=workflow
DB_USERNAME=postgres
DB_PASSWORD=admin

# Flowable REST API Configuration
FLOWABLE_REST_URL=http://flowable-rest:8080/flowable-rest
FLOWABLE_REST_USERNAME=rest-admin
FLOWABLE_REST_PASSWORD=test
```

### Service Ports

- **PostgreSQL**: 5432
- **Flowable REST API**: 8080
- **Workflow Integration**: 8082
- **Workplace Learning**: 8091
- **BPMN Viewer**: 3001

## 📊 Usage

### 1. View BPMN Processes

1. Open http://localhost:3001
2. Navigate to the "Process Definitions" tab
3. Click on any process to view its BPMN diagram

### 2. Start Process Instances

1. In the BPMN viewer, click "Start Instance"
2. Provide any required variables
3. The process will start and appear in "Process Instances" tab

### 3. View Active Tasks with Highlighting

1. Go to "Process Instances" tab
2. Click on an active process instance
3. The BPMN diagram will show with **highlighted active tasks**
4. Active tasks are highlighted in green with thicker borders

### 4. Monitor Workflow Progress

- Active tasks are highlighted in real-time
- Process instance status updates automatically
- View process variables and execution history

## 🔗 API Endpoints

### Unified Workflow API (Port 8082)

```
GET  /api/v1/workflow/bpmn/unified/definitions
GET  /api/v1/workflow/bpmn/unified/definition/{id}/xml-with-tasks?processInstanceId={id}
GET  /api/v1/workflow/bpmn/unified/instances
GET  /api/v1/workflow/bpmn/unified/instances/history
GET  /api/v1/workflow/bpmn/unified/instance/{id}/details
POST /api/v1/workflow/bpmn/unified/start-instance/{processKey}
```

### Flowable REST API (Port 8080)

```
GET  /flowable-rest/service/repository/process-definitions
GET  /flowable-rest/service/runtime/process-instances
GET  /flowable-rest/service/runtime/tasks
POST /flowable-rest/service/runtime/process-instances
```

## 🧪 Testing

### 1. Test API Connectivity

```bash
# Test workflow integration API
curl http://localhost:8082/api/v1/workflow/bpmn/unified/definitions

# Test Flowable REST API
curl -u rest-admin:test http://localhost:8080/flowable-rest/service/repository/process-definitions
```

### 2. Test BPMN Viewer

1. Open http://localhost:3001
2. Verify process definitions load
3. Start a process instance
4. Verify active task highlighting works

### 3. Test Database Connection

```bash
# Connect to PostgreSQL
docker exec -it workflow-postgres psql -U postgres -d workflow

# Check Flowable tables
\dt act_*
```

## 🐛 Troubleshooting

### Common Issues

1. **Services not starting**: Check Docker logs
   ```bash
   docker-compose logs [service-name]
   ```

2. **Database connection issues**: Ensure PostgreSQL is healthy
   ```bash
   docker-compose ps postgres
   ```

3. **Flowable REST API not responding**: Wait for initialization
   ```bash
   docker-compose logs flowable-rest
   ```

4. **BPMN viewer not loading**: Check network proxy configuration

### Reset Everything

```bash
# Stop all services and remove volumes
docker-compose down -v

# Rebuild and restart
docker-compose up --build -d
```

## 📁 Project Structure

```
├── docker-compose.yml              # Main orchestration file
├── init-scripts/                   # Database initialization
├── workflow-integration/           # Spring Boot workflow service
│   ├── src/main/resources/processes/  # BPMN files
│   └── Dockerfile
├── workflow-bpmn-viewer/          # React frontend
│   ├── src/components/
│   ├── src/services/
│   └── Dockerfile
├── workplace-learning/            # Additional service
└── README.md
```

## 🎯 Key Features Implemented

✅ **Docker Compose setup with all services**  
✅ **Flowable REST API on port 8080**  
✅ **PostgreSQL database integration**  
✅ **Unified API endpoints for BPMN viewer**  
✅ **Real-time active task highlighting**  
✅ **BPMN diagram visualization**  
✅ **Process instance management**  
✅ **Workflow integration service**  

## 🔄 Next Steps

- Add user authentication and authorization
- Implement task completion workflows
- Add process monitoring dashboards
- Enhance error handling and logging
- Add automated testing suite

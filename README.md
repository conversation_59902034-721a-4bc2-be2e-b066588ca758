# BPMN Workflow System with Flowable REST API

A complete BPMN workflow management system featuring:
- **Flowable REST API** running on Docker port 8080
- **React BPMN.js Viewer** for interactive workflow visualization
- **Active Task Highlighting** showing current workflow steps
- **PostgreSQL Database** for workflow persistence
- **Workplace Learning Integration** via Feign clients

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  BPMN Viewer    │    │  Workflow        │    │  Workplace      │
│  Frontend       │◄──►│  Integration     │◄──►│  Learning       │
│  (Port 3001)    │    │  (Port 8080)     │    │  (Port 8091)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  PostgreSQL     │
                       │  Database       │
                       │  (Port 5432)    │
                       └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Git

### 1. Clone and Start
```bash
# Clone the repository
git clone <repository-url>
cd <repository-name>

# Start the complete system (Linux/Mac)
chmod +x start-workflow-system.sh
./start-workflow-system.sh

# Or on Windows
start-workflow-system.bat
```

### 2. Access the System
- **BPMN Viewer**: http://localhost:3001
- **Workflow API**: http://localhost:8080
- **Workplace Learning**: http://localhost:8091

## 📋 Features

### BPMN Viewer Frontend
- Interactive BPMN diagram visualization
- Real-time active task highlighting with pulse animation
- Process instance monitoring and status tracking
- Export diagrams as SVG or download BPMN XML
- Responsive design with Tailwind CSS

### Workflow Integration Service
- Flowable BPMN engine integration
- Automatic BPMN process deployment on startup
- RESTful API for process management
- Process instance lifecycle management
- Active task tracking and highlighting

### Database Integration
- PostgreSQL database with automatic schema creation
- Process instance persistence
- Task and execution history
- Docker volume for data persistence

## 🔧 API Endpoints

### Process Definitions
```bash
# Get all process definitions
GET /api/v1/workflow/bpmn/definitions

# Get BPMN XML for a specific definition
GET /api/v1/workflow/bpmn/definition/{id}/xml
```

### Process Instances
```bash
# Get active process instances
GET /api/v1/workflow/bpmn/instances

# Get historic process instances
GET /api/v1/workflow/bpmn/instances/history

# Get process instance status and active tasks
GET /api/v1/workflow/bpmn/instance/{id}/status
```

### Process Management
```bash
# Start a new process
GET /api/v1/workflow/start-process/{applicationType}/{applicationNumber}

# Resume a process with signal
POST /api/v1/workflow/resume-process/{processInstanceId}/{signalType}
```

## 📁 BPMN Process Files

The system automatically deploys BPMN processes from:
```
workflow-integration/src/main/resources/processes/
├── check-demo.bpmn20.xml          # HRDC Workflow
├── check-demo1.bpmn20.xml         # Complaint Workflow  
└── Appeal_Workflow.bpmn20.xml     # Appeal Workflow
```

## 🎨 Active Task Highlighting

The BPMN viewer provides visual feedback for workflow execution:

- **🔵 Current Tasks**: Blue highlight with pulse animation
- **🟢 Active Elements**: Green highlight for active workflow steps
- **⚪ Completed Tasks**: Faded green for completed elements

## 🧪 Testing the System

### 1. Test API Connectivity
```bash
# Check if services are running
curl http://localhost:8080/api/v1/workflow/bpmn/definitions
curl http://localhost:8091/actuator/health
```

### 2. Start a Workflow Process
```bash
# Start HRDC workflow
curl "http://localhost:8080/api/v1/workflow/start-process/HRDC/APP123"

# Start Complaint workflow
curl -X POST "http://localhost:8080/api/v1/complaintWorkflow/startprocess/COMP456"
```

### 3. View in BPMN Viewer
1. Open http://localhost:3001
2. Select a process definition
3. View active process instances
4. See highlighted active tasks in the diagram

## 🔧 Configuration

### Environment Variables
The system supports environment-based configuration:

```yaml
# Database Configuration
SPRING_DATASOURCE_URL=*************************************************
SPRING_DATASOURCE_USERNAME=postgres
SPRING_DATASOURCE_PASSWORD=admin

# Flowable Configuration
FLOWABLE_DATABASE_SCHEMA_UPDATE=true
FLOWABLE_ASYNC_EXECUTOR_ACTIVATE=true
FLOWABLE_REST_APP_ENABLED=true

# Server Configuration
SERVER_PORT=8080
```

## 📝 Development

### Adding New BPMN Processes
1. Place BPMN files in `workflow-integration/src/main/resources/processes/`
2. Update `ProcessDeploymentConfig.java` to include new files
3. Restart the system

### Frontend Development
```bash
cd workflow-bpmn-viewer
npm install
npm run dev
```

### Backend Development
```bash
cd workflow-integration
mvn spring-boot:run
```

## 🛑 Stopping the System

```bash
# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

## 📊 Monitoring

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f workflow-integration
docker-compose logs -f workflow-bpmn-viewer
docker-compose logs -f workplace-learning
```

### Database Access
```bash
# Connect to PostgreSQL
docker exec -it postgres-workflow psql -U postgres -d workflow
```

## 🔍 Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3001, 8080, 8091, 5432 are available
2. **Docker memory**: Increase Docker memory allocation if services fail to start
3. **Database connection**: Check PostgreSQL container health status

### Reset System
```bash
# Clean restart with fresh database
./start-workflow-system.sh --clean
```

## 📚 Additional Resources

- [Flowable Documentation](https://flowable.com/open-source/docs/)
- [BPMN.js Documentation](https://bpmn.io/toolkit/bpmn-js/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)

---

🎉 **Your BPMN Workflow System is ready!** Open http://localhost:3001 to start viewing and managing your workflows.

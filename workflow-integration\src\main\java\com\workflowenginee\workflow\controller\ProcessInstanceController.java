package com.workflowenginee.workflow.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

@RestController
@RequestMapping("/api/v1/workflow")
@CrossOrigin(origins = "*")
@Slf4j
public class ProcessInstanceController {

    @Autowired
    private org.flowable.engine.RuntimeService runtimeService;

    @Autowired
    private org.flowable.engine.HistoryService historyService;

    @Autowired
    private org.flowable.engine.TaskService taskService;

    @Autowired
    private org.flowable.engine.RepositoryService repositoryService;

    /**
     * Get all process instances
     */
    @GetMapping("/instances")
    public ResponseEntity<?> getAllProcessInstances() {
        try {
            System.out.println("Fetching all process instances");
            
            List<org.flowable.engine.runtime.ProcessInstance> processInstances = runtimeService
                .createProcessInstanceQuery()
                .list();
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (org.flowable.engine.runtime.ProcessInstance instance : processInstances) {
                Map<String, Object> instanceData = new HashMap<>();
                instanceData.put("id", instance.getId());
                instanceData.put("processDefinitionId", instance.getProcessDefinitionId());
                instanceData.put("processDefinitionKey", instance.getProcessDefinitionKey());
                instanceData.put("processDefinitionName", instance.getProcessDefinitionName());
                instanceData.put("businessKey", instance.getBusinessKey());
                instanceData.put("startTime", instance.getStartTime());
                instanceData.put("startUserId", instance.getStartUserId());
                instanceData.put("suspended", instance.isSuspended());
                instanceData.put("ended", instance.isEnded());
                instanceData.put("status", instance.isEnded() ? "COMPLETED" : (instance.isSuspended() ? "SUSPENDED" : "ACTIVE"));

                // Get current activities (active tasks) for this process instance
                List<String> currentActivities = new ArrayList<>();
                try {
                    List<org.flowable.task.api.Task> tasks = taskService.createTaskQuery()
                        .processInstanceId(instance.getId())
                        .list();
                    for (org.flowable.task.api.Task task : tasks) {
                        currentActivities.add(task.getTaskDefinitionKey());
                    }
                } catch (Exception e) {
                    System.out.println("Error getting current activities for instance " + instance.getId() + ": " + e.getMessage());
                }
                instanceData.put("currentActivities", currentActivities);

                result.add(instanceData);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result);
            response.put("total", result.size());
            
            System.out.println("Found " + result.size() + " process instances");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("Error fetching process instances: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching process instances: " + e.getMessage()
            ));
        }
    }

    /**
     * Get historic process instances with enhanced data for diagram viewing
     */
    @GetMapping("/instances/history")
    public ResponseEntity<?> getHistoricProcessInstances() {
        try {
            System.out.println("Fetching historic process instances");

            List<org.flowable.engine.history.HistoricProcessInstance> historicInstances = historyService
                .createHistoricProcessInstanceQuery()
                .finished()
                .orderByProcessInstanceEndTime()
                .desc()
                .list();

            List<Map<String, Object>> result = new ArrayList<>();
            for (org.flowable.engine.history.HistoricProcessInstance instance : historicInstances) {
                Map<String, Object> instanceData = new HashMap<>();
                instanceData.put("id", instance.getId());
                instanceData.put("processDefinitionId", instance.getProcessDefinitionId());
                instanceData.put("processDefinitionKey", instance.getProcessDefinitionKey());
                instanceData.put("processDefinitionName", instance.getProcessDefinitionName());
                instanceData.put("businessKey", instance.getBusinessKey());
                instanceData.put("startTime", instance.getStartTime());
                instanceData.put("endTime", instance.getEndTime());
                instanceData.put("startUserId", instance.getStartUserId());
                instanceData.put("status", "COMPLETED");
                instanceData.put("ended", true);
                instanceData.put("suspended", false);

                // Calculate duration
                if (instance.getStartTime() != null && instance.getEndTime() != null) {
                    long duration = instance.getEndTime().getTime() - instance.getStartTime().getTime();
                    instanceData.put("durationInMillis", duration);
                }

                // Get historic activities for completed path visualization
                List<org.flowable.engine.history.HistoricActivityInstance> activities = historyService
                    .createHistoricActivityInstanceQuery()
                    .processInstanceId(instance.getId())
                    .finished()
                    .orderByHistoricActivityInstanceStartTime()
                    .asc()
                    .list();

                List<String> completedActivities = new ArrayList<>();
                for (org.flowable.engine.history.HistoricActivityInstance activity : activities) {
                    if (activity.getActivityType() != null &&
                        (activity.getActivityType().equals("userTask") ||
                         activity.getActivityType().equals("serviceTask") ||
                         activity.getActivityType().equals("scriptTask"))) {
                        completedActivities.add(activity.getActivityId());
                    }
                }
                instanceData.put("completedActivities", completedActivities);
                instanceData.put("currentActivities", new ArrayList<>()); // Empty for completed instances

                result.add(instanceData);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result);
            response.put("total", result.size());
            
            System.out.println("Found " + result.size() + " historic process instances");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("Error fetching historic process instances: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching historic process instances: " + e.getMessage()
            ));
        }
    }

    /**
     * Get process instance status and details
     */
    @GetMapping("/instance/{processInstanceId}/status")
    public ResponseEntity<?> getProcessInstanceStatus(@PathVariable String processInstanceId) {
        try {
            System.out.println("Fetching status for process instance: " + processInstanceId);
            
            // Try to get active process instance first
            org.flowable.engine.runtime.ProcessInstance processInstance = runtimeService
                .createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
            
            Map<String, Object> statusData = new HashMap<>();
            
            if (processInstance != null) {
                // Active process instance
                statusData.put("id", processInstance.getId());
                statusData.put("processDefinitionId", processInstance.getProcessDefinitionId());
                statusData.put("processDefinitionKey", processInstance.getProcessDefinitionKey());
                statusData.put("processDefinitionName", processInstance.getProcessDefinitionName());
                statusData.put("businessKey", processInstance.getBusinessKey());
                statusData.put("startTime", processInstance.getStartTime());
                statusData.put("startUserId", processInstance.getStartUserId());
                statusData.put("suspended", processInstance.isSuspended());
                statusData.put("ended", false);
                statusData.put("status", processInstance.isSuspended() ? "SUSPENDED" : "ACTIVE");
                
                // Get current activities
                List<org.flowable.engine.runtime.Execution> executions = runtimeService
                    .createExecutionQuery()
                    .processInstanceId(processInstanceId)
                    .list();
                
                List<String> currentActivities = new ArrayList<>();
                for (org.flowable.engine.runtime.Execution execution : executions) {
                    if (execution.getActivityId() != null) {
                        currentActivities.add(execution.getActivityId());
                    }
                }
                statusData.put("currentActivities", currentActivities);
                
            } else {
                // Check if it's a historic process instance
                org.flowable.engine.history.HistoricProcessInstance historicInstance = historyService
                    .createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();
                
                if (historicInstance != null) {
                    statusData.put("id", historicInstance.getId());
                    statusData.put("processDefinitionId", historicInstance.getProcessDefinitionId());
                    statusData.put("processDefinitionKey", historicInstance.getProcessDefinitionKey());
                    statusData.put("processDefinitionName", historicInstance.getProcessDefinitionName());
                    statusData.put("businessKey", historicInstance.getBusinessKey());
                    statusData.put("startTime", historicInstance.getStartTime());
                    statusData.put("endTime", historicInstance.getEndTime());
                    statusData.put("startUserId", historicInstance.getStartUserId());
                    statusData.put("suspended", false);
                    statusData.put("ended", true);
                    statusData.put("status", "COMPLETED");
                    statusData.put("currentActivities", new ArrayList<>());
                } else {
                    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                        "success", false,
                        "message", "Process instance not found: " + processInstanceId
                    ));
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statusData);
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("Error fetching process instance status: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching process instance status: " + e.getMessage()
            ));
        }
    }

    /**
     * Suspend a process instance
     */
    @PostMapping("/instance/{processInstanceId}/suspend")
    public ResponseEntity<?> suspendProcessInstance(@PathVariable String processInstanceId) {
        try {
            System.out.println("Suspending process instance: " + processInstanceId);
            
            runtimeService.suspendProcessInstanceById(processInstanceId);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Process instance suspended successfully",
                "processInstanceId", processInstanceId
            ));

        } catch (Exception e) {
            System.err.println("Error suspending process instance: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error suspending process instance: " + e.getMessage()
            ));
        }
    }

    /**
     * Activate a suspended process instance
     */
    @PostMapping("/instance/{processInstanceId}/activate")
    public ResponseEntity<?> activateProcessInstance(@PathVariable String processInstanceId) {
        try {
            System.out.println("Activating process instance: " + processInstanceId);
            
            runtimeService.activateProcessInstanceById(processInstanceId);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Process instance activated successfully",
                "processInstanceId", processInstanceId
            ));

        } catch (Exception e) {
            System.err.println("Error activating process instance: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error activating process instance: " + e.getMessage()
            ));
        }
    }

    /**
     * Delete a process instance
     */
    @PostMapping("/instance/{processInstanceId}/delete")
    public ResponseEntity<?> deleteProcessInstance(
            @PathVariable String processInstanceId,
            @RequestBody(required = false) Map<String, Object> requestBody) {
        try {
            System.out.println("Deleting process instance: " + processInstanceId);
            
            String deleteReason = "Deleted via BPMN Viewer";
            if (requestBody != null && requestBody.containsKey("deleteReason")) {
                deleteReason = (String) requestBody.get("deleteReason");
            }
            
            runtimeService.deleteProcessInstance(processInstanceId, deleteReason);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Process instance deleted successfully",
                "processInstanceId", processInstanceId,
                "deleteReason", deleteReason
            ));

        } catch (Exception e) {
            System.err.println("Error deleting process instance: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error deleting process instance: " + e.getMessage()
            ));
        }
    }

    /**
     * Get workflow diagram with active task highlighting for BPMN viewer
     * This endpoint provides all data needed for frontend diagram visualization
     */
    @GetMapping("/diagram/{processDefinitionKey}")
    public ResponseEntity<?> getWorkflowDiagram(@PathVariable String processDefinitionKey) {
        try {
            System.out.println("Fetching workflow diagram for process: " + processDefinitionKey);

            // Get the latest process definition
            org.flowable.engine.repository.ProcessDefinition processDefinition = repositoryService
                .createProcessDefinitionQuery()
                .processDefinitionKey(processDefinitionKey)
                .latestVersion()
                .singleResult();

            if (processDefinition == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                    "success", false,
                    "message", "Process definition not found: " + processDefinitionKey
                ));
            }

            // Get BPMN XML
            InputStream bpmnStream = repositoryService.getResourceAsStream(
                processDefinition.getDeploymentId(),
                processDefinition.getResourceName()
            );
            String bpmnXml = new String(bpmnStream.readAllBytes(), StandardCharsets.UTF_8);

            // Get all active instances for this process
            List<org.flowable.engine.runtime.ProcessInstance> activeInstances = runtimeService
                .createProcessInstanceQuery()
                .processDefinitionKey(processDefinitionKey)
                .list();

            List<Map<String, Object>> instancesData = new ArrayList<>();
            Set<String> allActiveActivities = new HashSet<>();

            for (org.flowable.engine.runtime.ProcessInstance instance : activeInstances) {
                Map<String, Object> instanceData = new HashMap<>();
                instanceData.put("id", instance.getId());
                instanceData.put("businessKey", instance.getBusinessKey());
                instanceData.put("startTime", instance.getStartTime());
                instanceData.put("suspended", instance.isSuspended());
                instanceData.put("status", instance.isSuspended() ? "SUSPENDED" : "ACTIVE");

                // Get current activities for this instance
                List<org.flowable.engine.runtime.Execution> executions = runtimeService
                    .createExecutionQuery()
                    .processInstanceId(instance.getId())
                    .list();

                List<String> currentActivities = new ArrayList<>();
                for (org.flowable.engine.runtime.Execution execution : executions) {
                    if (execution.getActivityId() != null) {
                        currentActivities.add(execution.getActivityId());
                        allActiveActivities.add(execution.getActivityId());
                    }
                }
                instanceData.put("currentActivities", currentActivities);
                instancesData.add(instanceData);
            }

            // Prepare response with all diagram data
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("processDefinition", Map.of(
                "id", processDefinition.getId(),
                "key", processDefinition.getKey(),
                "name", processDefinition.getName(),
                "version", processDefinition.getVersion(),
                "deploymentId", processDefinition.getDeploymentId(),
                "resourceName", processDefinition.getResourceName()
            ));
            response.put("bpmnXml", bpmnXml);
            response.put("activeInstances", instancesData);
            response.put("allActiveActivities", new ArrayList<>(allActiveActivities));
            response.put("totalActiveInstances", activeInstances.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("Error fetching workflow diagram: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching workflow diagram: " + e.getMessage()
            ));
        }
    }

    /**
     * 🎯 SINGLE UNIFIED API FOR FRONTEND WORKFLOW INTEGRATION
     * This is the ONE API endpoint that provides everything the frontend needs:
     * - BPMN resource files list
     * - Process definitions (deployed and resources)
     * - Active process instances with current activities
     * - BPMN XML content for diagram rendering
     * - Complete workflow state for visualization
     */
    @GetMapping("/workflow-viewer-data")
    public ResponseEntity<?> getWorkflowViewerData(
            @RequestParam(required = false) String processKey,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false, defaultValue = "false") boolean deployResource) {

        try {
            System.out.println("🎯 Fetching unified workflow viewer data");

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("timestamp", new Date());

            // 1. Get all BPMN resource files
            List<Map<String, Object>> resourceFiles = getBpmnResourceFiles();
            response.put("availableResources", resourceFiles);

            // 2. Get all deployed process definitions
            List<org.flowable.engine.repository.ProcessDefinition> deployedDefinitions = repositoryService
                .createProcessDefinitionQuery()
                .latestVersion()
                .list();

            List<Map<String, Object>> processDefinitions = new ArrayList<>();
            for (org.flowable.engine.repository.ProcessDefinition def : deployedDefinitions) {
                Map<String, Object> defData = new HashMap<>();
                defData.put("id", def.getId());
                defData.put("key", def.getKey());
                defData.put("name", def.getName());
                defData.put("version", def.getVersion());
                defData.put("deploymentId", def.getDeploymentId());
                defData.put("resourceName", def.getResourceName());
                defData.put("category", def.getCategory());
                defData.put("suspended", def.isSuspended());
                defData.put("source", "deployed");
                processDefinitions.add(defData);
            }
            response.put("processDefinitions", processDefinitions);

            // 3. Deploy resource if requested
            if (deployResource && fileName != null && !fileName.trim().isEmpty()) {
                System.out.println("🚀 Deploying resource: " + fileName);

                String resourcePath = "processes/" + fileName;
                Resource resource = new ClassPathResource(resourcePath);

                if (resource.exists()) {
                    try (InputStream inputStream = resource.getInputStream()) {
                        org.flowable.engine.repository.Deployment deployment = repositoryService
                            .createDeployment()
                            .name("Auto-deployment: " + fileName)
                            .addInputStream(fileName, inputStream)
                            .deploy();

                        org.flowable.engine.repository.ProcessDefinition newDef = repositoryService
                            .createProcessDefinitionQuery()
                            .deploymentId(deployment.getId())
                            .singleResult();

                        if (newDef != null) {
                            processKey = newDef.getKey(); // Use the newly deployed process
                            response.put("deploymentResult", Map.of(
                                "success", true,
                                "deploymentId", deployment.getId(),
                                "processKey", newDef.getKey(),
                                "processName", newDef.getName()
                            ));
                        }
                    }
                } else {
                    response.put("deploymentResult", Map.of(
                        "success", false,
                        "message", "Resource file not found: " + fileName
                    ));
                }
            }

            // 4. Get specific process data if processKey is provided
            if (processKey != null && !processKey.trim().isEmpty()) {
                System.out.println("📊 Fetching data for process: " + processKey);

                // Get process definition
                org.flowable.engine.repository.ProcessDefinition processDefinition = repositoryService
                    .createProcessDefinitionQuery()
                    .processDefinitionKey(processKey)
                    .latestVersion()
                    .singleResult();

                if (processDefinition != null) {
                    // Get BPMN XML
                    InputStream bpmnStream = repositoryService.getResourceAsStream(
                        processDefinition.getDeploymentId(),
                        processDefinition.getResourceName()
                    );
                    String bpmnXml = new String(bpmnStream.readAllBytes(), StandardCharsets.UTF_8);

                    // Get active instances
                    List<org.flowable.engine.runtime.ProcessInstance> activeInstances = runtimeService
                        .createProcessInstanceQuery()
                        .processDefinitionKey(processKey)
                        .list();

                    List<Map<String, Object>> instancesData = new ArrayList<>();
                    Set<String> allActiveActivities = new HashSet<>();

                    for (org.flowable.engine.runtime.ProcessInstance instance : activeInstances) {
                        Map<String, Object> instanceData = new HashMap<>();
                        instanceData.put("id", instance.getId());
                        instanceData.put("businessKey", instance.getBusinessKey());
                        instanceData.put("startTime", instance.getStartTime());
                        instanceData.put("suspended", instance.isSuspended());
                        instanceData.put("status", instance.isSuspended() ? "SUSPENDED" : "ACTIVE");

                        // Get current activities
                        List<org.flowable.engine.runtime.Execution> executions = runtimeService
                            .createExecutionQuery()
                            .processInstanceId(instance.getId())
                            .list();

                        List<String> currentActivities = new ArrayList<>();
                        for (org.flowable.engine.runtime.Execution execution : executions) {
                            if (execution.getActivityId() != null) {
                                currentActivities.add(execution.getActivityId());
                                allActiveActivities.add(execution.getActivityId());
                            }
                        }
                        instanceData.put("currentActivities", currentActivities);
                        instancesData.add(instanceData);
                    }

                    // Get historic instances for completed workflows
                    List<org.flowable.engine.history.HistoricProcessInstance> historicInstances = historyService
                        .createHistoricProcessInstanceQuery()
                        .processDefinitionKey(processKey)
                        .finished()
                        .orderByProcessInstanceEndTime()
                        .desc()
                        .listPage(0, 10); // Last 10 completed instances

                    List<Map<String, Object>> historicData = new ArrayList<>();
                    for (org.flowable.engine.history.HistoricProcessInstance historic : historicInstances) {
                        Map<String, Object> histData = new HashMap<>();
                        histData.put("id", historic.getId());
                        histData.put("businessKey", historic.getBusinessKey());
                        histData.put("startTime", historic.getStartTime());
                        histData.put("endTime", historic.getEndTime());
                        histData.put("status", "COMPLETED");

                        if (historic.getStartTime() != null && historic.getEndTime() != null) {
                            long duration = historic.getEndTime().getTime() - historic.getStartTime().getTime();
                            histData.put("durationInMillis", duration);
                        }
                        historicData.add(histData);
                    }

                    // Build complete process data
                    Map<String, Object> processData = new HashMap<>();
                    processData.put("definition", Map.of(
                        "id", processDefinition.getId(),
                        "key", processDefinition.getKey(),
                        "name", processDefinition.getName(),
                        "version", processDefinition.getVersion(),
                        "deploymentId", processDefinition.getDeploymentId(),
                        "resourceName", processDefinition.getResourceName(),
                        "category", processDefinition.getCategory(),
                        "suspended", processDefinition.isSuspended()
                    ));
                    processData.put("bpmnXml", bpmnXml);
                    processData.put("activeInstances", instancesData);
                    processData.put("historicInstances", historicData);
                    processData.put("allActiveActivities", new ArrayList<>(allActiveActivities));
                    processData.put("totalActiveInstances", activeInstances.size());
                    processData.put("totalHistoricInstances", historicInstances.size());

                    response.put("processData", processData);
                } else {
                    response.put("processData", null);
                    response.put("message", "Process definition not found: " + processKey);
                }
            }

            // 5. Get overall system statistics
            long totalActiveInstances = runtimeService.createProcessInstanceQuery().count();
            long totalCompletedInstances = historyService.createHistoricProcessInstanceQuery().finished().count();
            long totalDeployments = repositoryService.createDeploymentQuery().count();

            response.put("systemStats", Map.of(
                "totalActiveInstances", totalActiveInstances,
                "totalCompletedInstances", totalCompletedInstances,
                "totalDeployments", totalDeployments,
                "totalProcessDefinitions", processDefinitions.size(),
                "totalResourceFiles", resourceFiles.size()
            ));

            System.out.println("✅ Successfully prepared unified workflow viewer data");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("❌ Error fetching workflow viewer data: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching workflow viewer data: " + e.getMessage(),
                "timestamp", new Date()
            ));
        }
    }

    /**
     * 🚀 START PROCESS INSTANCE AND GET UPDATED WORKFLOW DATA
     * Companion API to start new instances and return updated workflow state
     */
    @PostMapping("/workflow-viewer-action")
    public ResponseEntity<?> workflowViewerAction(
            @RequestParam String action,
            @RequestParam String processKey,
            @RequestParam(required = false) String processInstanceId,
            @RequestBody(required = false) Map<String, Object> variables) {

        try {
            System.out.println("🎯 Executing workflow action: " + action + " for process: " + processKey);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("action", action);
            response.put("processKey", processKey);
            response.put("timestamp", new Date());

            switch (action.toLowerCase()) {
                case "start":
                    // Start new process instance
                    org.flowable.engine.runtime.ProcessInstance newInstance = runtimeService
                        .startProcessInstanceByKey(processKey, variables != null ? variables : new HashMap<>());

                    response.put("actionResult", Map.of(
                        "instanceId", newInstance.getId(),
                        "businessKey", newInstance.getBusinessKey(),
                        "startTime", newInstance.getStartTime(),
                        "message", "Process instance started successfully"
                    ));
                    break;

                case "suspend":
                    if (processInstanceId != null) {
                        runtimeService.suspendProcessInstanceById(processInstanceId);
                        response.put("actionResult", Map.of(
                            "instanceId", processInstanceId,
                            "message", "Process instance suspended successfully"
                        ));
                    }
                    break;

                case "activate":
                    if (processInstanceId != null) {
                        runtimeService.activateProcessInstanceById(processInstanceId);
                        response.put("actionResult", Map.of(
                            "instanceId", processInstanceId,
                            "message", "Process instance activated successfully"
                        ));
                    }
                    break;

                case "delete":
                    if (processInstanceId != null) {
                        String deleteReason = variables != null && variables.containsKey("deleteReason")
                            ? (String) variables.get("deleteReason")
                            : "Deleted via workflow viewer";
                        runtimeService.deleteProcessInstance(processInstanceId, deleteReason);
                        response.put("actionResult", Map.of(
                            "instanceId", processInstanceId,
                            "deleteReason", deleteReason,
                            "message", "Process instance deleted successfully"
                        ));
                    }
                    break;

                default:
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of(
                        "success", false,
                        "message", "Unknown action: " + action + ". Supported actions: start, suspend, activate, delete"
                    ));
            }

            // Return updated workflow data after action
            ResponseEntity<?> updatedData = getWorkflowViewerData(processKey, null, false);
            if (updatedData.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> updatedResponse = (Map<String, Object>) updatedData.getBody();
                response.put("updatedWorkflowData", updatedResponse);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("❌ Error executing workflow action: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "action", action,
                "processKey", processKey,
                "message", "Error executing workflow action: " + e.getMessage(),
                "timestamp", new Date()
            ));
        }
    }

    /**
     * Helper method to get BPMN files from resources/processes folder
     */
    private List<Map<String, Object>> getBpmnResourceFiles() {
        List<Map<String, Object>> resourceFiles = new ArrayList<>();

        try {
            // List of known BPMN files in resources/processes
            String[] bpmnFiles = {
                "oneTaskProcess.bpmn20.xml",
                "check-demo.bpmn20.xml",
                "check-demo1.bpmn20.xml",
                "Appeal_Workflow.bpmn20.xml"
            };

            for (String fileName : bpmnFiles) {
                String resourcePath = "processes/" + fileName;
                Resource resource = new ClassPathResource(resourcePath);

                if (resource.exists()) {
                    try (InputStream inputStream = resource.getInputStream()) {
                        String content = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);

                        // Extract process information from BPMN XML
                        Map<String, Object> fileInfo = extractProcessInfoFromXml(content, fileName);
                        if (fileInfo != null) {
                            fileInfo.put("source", "resource");
                            fileInfo.put("fileName", fileName);
                            fileInfo.put("resourcePath", resourcePath);
                            resourceFiles.add(fileInfo);
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("Error reading BPMN resource files: " + e.getMessage());
        }

        return resourceFiles;
    }

    /**
     * Helper method to extract process information from BPMN XML
     */
    private Map<String, Object> extractProcessInfoFromXml(String bpmnXml, String fileName) {
        try {
            // Simple XML parsing to extract process information
            Map<String, Object> processInfo = new HashMap<>();

            // Extract process ID
            String processId = extractXmlAttribute(bpmnXml, "process", "id");
            if (processId != null) {
                processInfo.put("key", processId);
                processInfo.put("id", processId + ":resource");
            }

            // Extract process name
            String processName = extractXmlAttribute(bpmnXml, "process", "name");
            if (processName != null) {
                processInfo.put("name", processName);
            } else {
                processInfo.put("name", fileName.replace(".bpmn20.xml", ""));
            }

            processInfo.put("version", "resource");
            processInfo.put("deploymentId", "");
            processInfo.put("resourceName", fileName);
            processInfo.put("category", "");
            processInfo.put("description", "BPMN resource file");
            processInfo.put("suspended", false);
            processInfo.put("tenantId", "");

            return processInfo;

        } catch (Exception e) {
            System.err.println("Error extracting process info from XML: " + e.getMessage());
            return null;
        }
    }

    /**
     * Helper method to extract XML attribute value
     */
    private String extractXmlAttribute(String xml, String elementName, String attributeName) {
        try {
            String pattern = "<" + elementName + "\\s+[^>]*" + attributeName + "\\s*=\\s*[\"']([^\"']*)[\"']";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(xml);
            if (m.find()) {
                return m.group(1);
            }
        } catch (Exception e) {
            // Ignore parsing errors
        }
        return null;
    }
}

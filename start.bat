@echo off
echo 🚀 Starting BPMN Workflow System
echo ===============================

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker first.
    exit /b 1
)

echo ✅ Docker is running

REM Start the system
echo 🏗️ Building and starting services...
docker-compose up --build -d

echo ⏳ Waiting for services to start...
timeout /t 30 /nobreak >nul

REM Check if services are running
echo 🔍 Checking service status...

REM Check backend
curl -f http://localhost:8080/api/definitions >nul 2>&1
if errorlevel 1 (
    echo ❌ Backend API is not responding
) else (
    echo ✅ Backend API is running at http://localhost:8080/api
)

REM Check frontend
curl -f http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    echo ❌ Frontend is not responding
) else (
    echo ✅ Frontend is running at http://localhost:3000
)

echo.
echo 🎉 BPMN Workflow System is ready!
echo ================================
echo.
echo 📋 Access Points:
echo   🌐 Frontend: http://localhost:3000
echo   🔧 Backend API: http://localhost:8080/api
echo.
echo 🧪 Quick Test:
echo   curl http://localhost:8080/api/definitions
echo.
echo 🛑 To stop: docker-compose down
pause

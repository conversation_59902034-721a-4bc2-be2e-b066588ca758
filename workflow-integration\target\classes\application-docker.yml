server:
  port: 8082

spring:
  application:
    name: workflow
  kafka:
    bootstrap-servers: localhost:9092
    template:
      default-topic: notifications
  jackson:
    serialization:
      INDENT_OUTPUT: true
  datasource:
    url: jdbc:postgresql://${POSTGRES_HOST:postgres}:${POSTGRES_PORT:5432}/${POSTGRES_DB:workflow}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:admin}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: HikariCP
      idle-timeout: 30000
      max-lifetime: 60000
      connection-timeout: 30000
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  data:
    jpa:
      repositories:
        enabled: true
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 20MB

# Flowable Configuration
flowable:
  database-schema-update: true
  async-executor-activate: true
  database-schema: public

# Flowable REST API Configuration
flowable-rest:
  base-url: ${FLOWABLE_REST_URL:http://flowable-rest:8080/flowable-rest}
  username: ${FLOWABLE_REST_USERNAME:rest-admin}
  password: ${FLOWABLE_REST_PASSWORD:test}

logging:
  level:
    org.springframework:
      security: INFO
      web: INFO
    org:
      hibernate:
        SQL: DEBUG
    com.workflowenginee: DEBUG

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    gateway:
      enabled: true
  info:
    env:
      enabled: true

app:
  logging:
    level: "INFO"

error:
  whitelabel:
    enabled: true

-- Create multiple databases for the workflow system
-- This script runs when PostgreSQL container starts for the first time

-- Create workplace_learning database
SELECT 'CREATE DATABASE workplace_learning'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'workplace_learning')\gexec

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE workflow TO postgres;
GRANT ALL PRIVILEGES ON DATABASE workplace_learning TO postgres;

-- Connect to workflow database and create schema if needed
\c workflow;
CREATE SCHEMA IF NOT EXISTS public;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO public;

-- Connect to workplace_learning database and create schema if needed
\c workplace_learning;
CREATE SCHEMA IF NOT EXISTS public;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO public;

-- Switch back to workflow database for Flowable tables
\c workflow;

-- Note: Flowable will automatically create its tables when the service starts
-- This is controlled by FLOWABLE_DATABASE_SCHEMA_UPDATE=true in docker-compose.yml

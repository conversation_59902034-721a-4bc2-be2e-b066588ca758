﻿import axios from 'axios';
import { ProcessDefinition, ProcessInstance, WorkflowApiResponse, ProcessInstanceStatus } from '../types/workflow';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api/v1/workflow';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.config?.url, error.message);
    return Promise.reject(error);
  }
);

export const workflowApi = {
  // Process Definitions
  getProcessDefinitions: async (): Promise<ProcessDefinition[]> => {
    const response = await api.get<WorkflowApiResponse<ProcessDefinition[]>>('/bpmn/definitions');
    return response.data.data || [];
  },

  getProcessDefinitionXml: async (processDefinitionId: string): Promise<string> => {
    const response = await api.get(`/bpmn/definition/${processDefinitionId}/xml`, {
      headers: {
        'Accept': 'application/xml',
      },
    });
    return response.data;
  },

  // Process Instances
  getProcessInstances: async (): Promise<ProcessInstance[]> => {
    const response = await api.get<WorkflowApiResponse<ProcessInstance[]>>('/instances');
    return response.data.data || [];
  },

  getHistoricProcessInstances: async (): Promise<ProcessInstance[]> => {
    const response = await api.get<WorkflowApiResponse<ProcessInstance[]>>('/instances/history');
    return response.data.data || [];
  },

  getProcessInstanceStatus: async (processInstanceId: string): Promise<ProcessInstanceStatus> => {
    const response = await api.get<WorkflowApiResponse<ProcessInstanceStatus>>(`/instance/${processInstanceId}/status`);
    if (!response.data.data) {
      throw new Error(response.data.message || 'Failed to fetch process instance status');
    }
    return response.data.data;
  },

  // Process Instance Management
  suspendProcessInstance: async (processInstanceId: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/suspend`);
  },

  activateProcessInstance: async (processInstanceId: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/activate`);
  },

  deleteProcessInstance: async (processInstanceId: string, deleteReason?: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/delete`, {
      deleteReason: deleteReason || 'Deleted via BPMN Viewer',
    });
  },

  // Start Process
  startProcess: async (applicationType: string, applicationNumber: string): Promise<any> => {
    const response = await api.get(`/start-process/${applicationType}/${applicationNumber}`);
    return response.data;
  },

  // Resume Process
  resumeProcess: async (processInstanceId: string, signalType: string, payload: any): Promise<any> => {
    const response = await api.post(`/resume-process/${processInstanceId}/${signalType}`, payload);
    return response.data;
  },
};
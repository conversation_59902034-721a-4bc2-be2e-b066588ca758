import axios from 'axios';
import { ProcessDefinition, ProcessInstance, WorkflowApiResponse, ProcessInstanceStatus } from '../types/workflow';

const API_BASE_URL = '/api/v1/workflow';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const workflowApi = {
  // Process Definitions - Using Unified API
  getProcessDefinitions: async (): Promise<ProcessDefinition[]> => {
    const response = await api.get<WorkflowApiResponse<ProcessDefinition[]>>('/bpmn/unified/definitions');
    return response.data.data || [];
  },

  getProcessDefinitionXml: async (processDefinitionId: string, processInstanceId?: string): Promise<any> => {
    const url = `/bpmn/unified/definition/${processDefinitionId}/xml-with-tasks`;
    const params = processInstanceId ? { processInstanceId } : {};

    const response = await api.get(url, { params });
    return response.data;
  },

  // Process Instances - Using Unified API
  getProcessInstances: async (): Promise<ProcessInstance[]> => {
    const response = await api.get<WorkflowApiResponse<ProcessInstance[]>>('/bpmn/unified/instances');
    return response.data.data || [];
  },

  getHistoricProcessInstances: async (): Promise<ProcessInstance[]> => {
    const response = await api.get<WorkflowApiResponse<ProcessInstance[]>>('/bpmn/unified/instances/history');
    return response.data.data || [];
  },

  getProcessInstanceStatus: async (processInstanceId: string): Promise<ProcessInstanceStatus> => {
    const response = await api.get<WorkflowApiResponse<ProcessInstanceStatus>>(`/bpmn/unified/instance/${processInstanceId}/details`);
    if (!response.data.data) {
      throw new Error(response.data.message || 'Failed to fetch process instance status');
    }
    return response.data.data;
  },

  // Process Instance Management
  suspendProcessInstance: async (processInstanceId: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/suspend`);
  },

  activateProcessInstance: async (processInstanceId: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/activate`);
  },

  deleteProcessInstance: async (processInstanceId: string, deleteReason?: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/delete`, {
      deleteReason: deleteReason || 'Deleted via BPMN Viewer',
    });
  },

  // Start Process
  startProcess: async (applicationType: string, applicationNumber: string): Promise<any> => {
    const response = await api.get(`/start-process/${applicationType}/${applicationNumber}`);
    return response.data;
  },

  // Resume Process
  resumeProcess: async (processInstanceId: string, signalType: string, payload: any): Promise<any> => {
    const response = await api.post(`/resume-process/${processInstanceId}/${signalType}`, payload);
    return response.data;
  },

  // Process Definition Management - Using Unified API
  startProcessInstance: async (processDefinitionKey: string, variables?: Record<string, any>): Promise<ProcessInstance> => {
    const response = await api.post<WorkflowApiResponse<ProcessInstance>>(`/bpmn/unified/start-instance/${processDefinitionKey}`, variables || {});
    if (!response.data.data) {
      throw new Error(response.data.message || 'Failed to start process instance');
    }
    return response.data.data;
  },
};
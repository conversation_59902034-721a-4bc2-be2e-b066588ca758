import React, { useEffect, useRef, useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import BpmnJS from 'bpmn-js/lib/NavigatedViewer';
import { ArrowLeft, Download, RefreshCw, Maximize2 } from 'lucide-react';
import { workflowApi } from '../services/workflowApi';
import { ProcessDefinition, ProcessInstance } from '../types/workflow';

const BpmnViewer: React.FC = () => {
  const { processDefinitionId } = useParams<{ processDefinitionId: string }>();
  const containerRef = useRef<HTMLDivElement>(null);
  const viewerRef = useRef<BpmnJS | null>(null);
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processDefinition, setProcessDefinition] = useState<ProcessDefinition | null>(null);
  const [relatedInstances, setRelatedInstances] = useState<ProcessInstance[]>([]);
  const [selectedInstance, setSelectedInstance] = useState<ProcessInstance | null>(null);
  const [bpmnXml, setBpmnXml] = useState<string>('');
  const [activeTasks, setActiveTasks] = useState<string[]>([]);

  useEffect(() => {
    if (processDefinitionId) {
      loadBpmnData();
    }
  }, [processDefinitionId]);

  useEffect(() => {
    if (bpmnXml && containerRef.current) {
      initializeBpmnViewer();
    }
  }, [bpmnXml]);

  useEffect(() => {
    if (selectedInstance && viewerRef.current) {
      loadInstanceWithActiveTasks();
    }
  }, [selectedInstance]);

  const loadBpmnData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load process definition details
      const definitions = await workflowApi.getProcessDefinitions();
      const definition = definitions.find(d => d.id === processDefinitionId);
      
      if (!definition) {
        throw new Error('Process definition not found');
      }
      
      setProcessDefinition(definition);

      // Load BPMN XML with active tasks (if instance is selected)
      const xmlData = await workflowApi.getProcessDefinitionXml(processDefinitionId!, selectedInstance?.id);
      setBpmnXml(xmlData.bpmnXml);

      // Set active tasks if available
      if (xmlData.activeTaskIds) {
        setActiveTasks(xmlData.activeTaskIds);
      }

      // Load related instances
      const [activeInstances, historicInstances] = await Promise.all([
        workflowApi.getProcessInstances(),
        workflowApi.getHistoricProcessInstances(),
      ]);

      const related = [...activeInstances, ...historicInstances].filter(
        instance => instance.processDefinitionId === processDefinitionId
      );
      
      setRelatedInstances(related);
      
      // Select the first active instance by default
      const firstActive = related.find(i => i.status === 'ACTIVE');
      if (firstActive) {
        setSelectedInstance(firstActive);
      }

    } catch (err) {
      console.error('Error loading BPMN data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load BPMN data');
    } finally {
      setLoading(false);
    }
  };

  const loadInstanceWithActiveTasks = async () => {
    if (!selectedInstance || !processDefinitionId) return;

    try {
      // Load BPMN XML with active tasks for the selected instance
      const xmlData = await workflowApi.getProcessDefinitionXml(processDefinitionId, selectedInstance.id);

      // Update active tasks
      if (xmlData.activeTaskIds) {
        setActiveTasks(xmlData.activeTaskIds);
        // Trigger highlighting
        if (viewerRef.current) {
          highlightActiveElements();
        }
      }
    } catch (err) {
      console.error('Error loading instance with active tasks:', err);
    }
  };

  const initializeBpmnViewer = async () => {
    if (!containerRef.current) return;

    try {
      // Clean up existing viewer
      if (viewerRef.current) {
        viewerRef.current.destroy();
      }

      // Create new viewer
      const viewer = new BpmnJS({
        container: containerRef.current,
        width: '100%',
        height: '600px',
      });

      viewerRef.current = viewer;

      // Import BPMN diagram
      await viewer.importXML(bpmnXml);
      
      // Fit viewport to diagram
      const canvas = viewer.get('canvas') as any;
      canvas.zoom('fit-viewport');

      // Highlight active elements if instance is selected
      if (selectedInstance) {
        highlightActiveElements();
      }

    } catch (err) {
      console.error('Error initializing BPMN viewer:', err);
      setError('Failed to render BPMN diagram');
    }
  };

  const highlightActiveElements = () => {
    if (!viewerRef.current || activeTasks.length === 0) return;

    try {
      const canvas = viewerRef.current.get('canvas') as any;
      const elementRegistry = viewerRef.current.get('elementRegistry') as any;

      // Clear previous highlights
      elementRegistry.forEach((element: any) => {
        canvas.removeMarker(element.id, 'highlight-active');
      });

      // Highlight active tasks
      activeTasks.forEach(taskId => {
        const element = elementRegistry.get(taskId);
        if (element) {
          canvas.addMarker(taskId, 'highlight-active');
        }
      });

    } catch (err) {
      console.error('Error highlighting active elements:', err);
    }
  };

  const downloadBpmn = () => {
    if (!bpmnXml || !processDefinition) return;

    const blob = new Blob([bpmnXml], { type: 'application/xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${processDefinition.key}_v${processDefinition.version}.bpmn`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const downloadSvg = async () => {
    if (!viewerRef.current) return;

    try {
      const canvas = viewerRef.current.get('canvas');
      const svg = await canvas.saveSVG();
      
      const blob = new Blob([svg.svg], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${processDefinition?.key}_diagram.svg`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error downloading SVG:', err);
    }
  };

  const fitViewport = () => {
    if (viewerRef.current) {
      const canvas = viewerRef.current.get('canvas') as any;
      canvas.zoom('fit-viewport');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <Link
                  to="/"
                  className="text-sm font-medium text-red-800 hover:text-red-600"
                >
                  ← Back to Dashboard
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              to="/"
              className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mr-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {processDefinition?.name}
              </h1>
              <p className="text-sm text-gray-500">
                Key: {processDefinition?.key} | Version: {processDefinition?.version}
              </p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={fitViewport}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <Maximize2 className="w-4 h-4 mr-2" />
              Fit View
            </button>
            <button
              onClick={downloadSvg}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <Download className="w-4 h-4 mr-2" />
              SVG
            </button>
            <button
              onClick={downloadBpmn}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <Download className="w-4 h-4 mr-2" />
              BPMN
            </button>
            <button
              onClick={loadBpmnData}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* BPMN Viewer */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">BPMN Diagram</h2>
              {selectedInstance && (
                <p className="text-sm text-gray-500 mt-1">
                  Showing active elements for instance: {selectedInstance.id}
                </p>
              )}
            </div>
            <div className="p-4">
              <div ref={containerRef} className="bpmn-container border rounded" />
            </div>
          </div>
        </div>

        {/* Process Instances Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Process Instances</h2>
              <p className="text-sm text-gray-500">{relatedInstances.length} instances</p>
            </div>
            <div className="p-4 max-h-96 overflow-y-auto">
              {relatedInstances.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">
                  No instances found for this process
                </p>
              ) : (
                <div className="space-y-3">
                  {relatedInstances.map((instance) => (
                    <div
                      key={instance.id}
                      className={`p-3 border rounded cursor-pointer transition-colors ${
                        selectedInstance?.id === instance.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedInstance(instance)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          instance.status === 'ACTIVE' 
                            ? 'bg-green-100 text-green-800'
                            : instance.status === 'COMPLETED'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {instance.status}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 truncate">
                        ID: {instance.id}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Started: {new Date(instance.startTime).toLocaleDateString()}
                      </p>
                      {instance.currentActivities.length > 0 && (
                        <p className="text-xs text-blue-600 mt-1">
                          Activities: {instance.currentActivities.length}
                        </p>
                      )}
                      <Link
                        to={`/instance/${instance.id}`}
                        className="text-xs text-blue-600 hover:text-blue-800 mt-2 inline-block"
                        onClick={(e) => e.stopPropagation()}
                      >
                        View Details →
                      </Link>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BpmnViewer;
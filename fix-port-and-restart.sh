#!/bin/bash

echo "🔧 Fixing Port Configuration and Restarting Services"
echo "===================================================="

echo "📋 Port Configuration Summary:"
echo "  - Workflow Integration: Container runs on 8082, exposed as 8080"
echo "  - Frontend: Runs on 3001"
echo "  - Workplace Learning: Runs on 8091"
echo "  - PostgreSQL: Runs on 5432"
echo ""

echo "🛑 Stopping existing containers..."
docker-compose down

echo "🧹 Cleaning up..."
docker system prune -f

echo "🏗️ Rebuilding and starting services with correct port configuration..."
docker-compose up --build -d

echo "⏳ Waiting for services to start..."
sleep 30

echo "🔍 Checking service status..."

# Check workflow-integration service
echo "Testing Workflow Integration API..."
if curl -f http://localhost:8080/api/v1/workflow/bpmn/definitions > /dev/null 2>&1; then
    echo "✅ Workflow Integration API is working on port 8080"
else
    echo "❌ Workflow Integration API is not responding"
    echo "📋 Checking container logs..."
    docker-compose logs --tail=20 workflow-integration
fi

# Check frontend
echo "Testing Frontend..."
if curl -f http://localhost:3001 > /dev/null 2>&1; then
    echo "✅ Frontend is working on port 3001"
else
    echo "❌ Frontend is not responding"
    echo "📋 Checking container logs..."
    docker-compose logs --tail=20 workflow-bpmn-viewer
fi

echo ""
echo "🎯 Quick Test Commands:"
echo "# Test API directly:"
echo "curl http://localhost:8080/api/v1/workflow/bpmn/definitions"
echo ""
echo "# Start a test process:"
echo "curl -X POST http://localhost:8080/api/v1/workflow/bpmn/test/start-process/Hrdc_workflow"
echo ""
echo "# Check active instances:"
echo "curl http://localhost:8080/api/v1/workflow/bpmn/instances"
echo ""

echo "🌐 Open your browser to:"
echo "  Frontend Dashboard: http://localhost:3001"
echo ""

echo "📊 Check container status:"
echo "docker-compose ps"
echo ""

echo "📝 View logs if needed:"
echo "docker-compose logs -f workflow-integration"
echo "docker-compose logs -f workflow-bpmn-viewer"
echo ""

echo "✅ Port fix completed! Your services should now be accessible."

#!/bin/bash

# Test script to verify the workflow integration service starts correctly
# This script tests the application startup and basic functionality

echo "🧪 Testing Workflow Integration Service Startup..."
echo "=================================================="

# Function to check if service is running
check_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to start..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            echo "✅ $service_name is running!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - $service_name not ready yet..."
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $service_name failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Function to test API endpoint
test_endpoint() {
    local url=$1
    local endpoint_name=$2
    
    echo "🔍 Testing $endpoint_name..."
    
    response=$(curl -s -w "%{http_code}" "$url")
    http_code="${response: -3}"
    
    if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 404 ]; then
        echo "✅ $endpoint_name responded (HTTP $http_code)"
        return 0
    else
        echo "❌ $endpoint_name failed (HTTP $http_code)"
        return 1
    fi
}

# Start the application in the background
echo "🚀 Starting Workflow Integration Service..."
cd workflow-integration

# Check if Maven is available
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven is not installed or not in PATH"
    exit 1
fi

# Start the application
echo "📦 Building and starting the application..."
mvn spring-boot:run > ../startup.log 2>&1 &
APP_PID=$!

echo "🔄 Application started with PID: $APP_PID"
echo "📋 Logs are being written to startup.log"

# Wait for the application to start
sleep 5

# Check if the application is running
if ! kill -0 $APP_PID 2>/dev/null; then
    echo "❌ Application failed to start. Check startup.log for details:"
    tail -20 ../startup.log
    exit 1
fi

# Test basic health endpoint
check_service "http://localhost:8082/actuator/health" "Workflow Integration Service"
if [ $? -ne 0 ]; then
    echo "❌ Health check failed. Stopping application..."
    kill $APP_PID 2>/dev/null
    echo "📋 Last 20 lines of startup.log:"
    tail -20 ../startup.log
    exit 1
fi

# Test API endpoints
echo ""
echo "🔍 Testing API Endpoints..."
test_endpoint "http://localhost:8082/api/v1/workflow/bpmn/definitions" "Process Definitions API"
test_endpoint "http://localhost:8082/api/v1/workflow/bpmn/unified/definitions" "Unified Process Definitions API"
test_endpoint "http://localhost:8082/api/v1/workflow/bpmn/unified/instances" "Unified Process Instances API"

# Test configuration
echo ""
echo "🔧 Testing Configuration..."
if curl -s "http://localhost:8082/actuator/configprops" | grep -q "flowable-rest"; then
    echo "✅ Flowable REST configuration is loaded"
else
    echo "⚠️  Flowable REST configuration may not be loaded properly"
fi

# Stop the application
echo ""
echo "🛑 Stopping application..."
kill $APP_PID 2>/dev/null
wait $APP_PID 2>/dev/null

echo ""
echo "✅ Startup test completed successfully!"
echo "📋 Full logs are available in startup.log"
echo ""
echo "🎯 Next steps:"
echo "   1. Start the full Docker Compose stack: docker-compose up -d"
echo "   2. Run integration tests: npm test"
echo "   3. Access BPMN Viewer: http://localhost:3001"

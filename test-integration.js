#!/usr/bin/env node

/**
 * Integration Test Script for BPMN Workflow System
 * Tests the complete integration between Docker services, database, workflow integration, and BPMN viewer
 */

const axios = require('axios');

// Configuration
const WORKFLOW_API_BASE = 'http://localhost:8082/api/v1/workflow';
const FLOWABLE_API_BASE = 'http://localhost:8080/flowable-rest';
const BPMN_VIEWER_URL = 'http://localhost:3001';

// Test results
const results = {
  passed: 0,
  failed: 0,
  tests: []
};

// Helper function to log test results
function logTest(name, passed, message = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${name}${message ? ': ' + message : ''}`);
  
  results.tests.push({ name, passed, message });
  if (passed) results.passed++;
  else results.failed++;
}

// Helper function to make HTTP requests with timeout
async function makeRequest(url, options = {}) {
  try {
    const response = await axios({
      url,
      timeout: 10000,
      ...options
    });
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.message, 
      status: error.response?.status 
    };
  }
}

// Test functions
async function testWorkflowIntegrationAPI() {
  console.log('\n🔧 Testing Workflow Integration API...');
  
  // Test unified process definitions endpoint
  const definitionsResult = await makeRequest(`${WORKFLOW_API_BASE}/bpmn/unified/definitions`);
  logTest(
    'Unified Process Definitions API',
    definitionsResult.success && definitionsResult.data?.success,
    definitionsResult.success ? `Found ${definitionsResult.data?.data?.length || 0} definitions` : definitionsResult.error
  );

  // Test unified process instances endpoint
  const instancesResult = await makeRequest(`${WORKFLOW_API_BASE}/bpmn/unified/instances`);
  logTest(
    'Unified Process Instances API',
    instancesResult.success && instancesResult.data?.success,
    instancesResult.success ? `Found ${instancesResult.data?.data?.length || 0} instances` : instancesResult.error
  );

  // Test unified historic instances endpoint
  const historicResult = await makeRequest(`${WORKFLOW_API_BASE}/bpmn/unified/instances/history`);
  logTest(
    'Unified Historic Instances API',
    historicResult.success && historicResult.data?.success,
    historicResult.success ? `Found ${historicResult.data?.data?.length || 0} historic instances` : historicResult.error
  );

  return definitionsResult.data?.data || [];
}

async function testFlowableRESTAPI() {
  console.log('\n🌊 Testing Flowable REST API...');
  
  // Test Flowable engine status
  const engineResult = await makeRequest(`${FLOWABLE_API_BASE}/service/management/engine`, {
    auth: { username: 'rest-admin', password: 'test' }
  });
  logTest(
    'Flowable Engine Status',
    engineResult.success,
    engineResult.success ? 'Engine is running' : engineResult.error
  );

  // Test Flowable process definitions
  const definitionsResult = await makeRequest(`${FLOWABLE_API_BASE}/service/repository/process-definitions`, {
    auth: { username: 'rest-admin', password: 'test' }
  });
  logTest(
    'Flowable Process Definitions',
    definitionsResult.success,
    definitionsResult.success ? `Found ${definitionsResult.data?.data?.length || 0} definitions` : definitionsResult.error
  );

  // Test Flowable process instances
  const instancesResult = await makeRequest(`${FLOWABLE_API_BASE}/service/runtime/process-instances`, {
    auth: { username: 'rest-admin', password: 'test' }
  });
  logTest(
    'Flowable Process Instances',
    instancesResult.success,
    instancesResult.success ? `Found ${instancesResult.data?.data?.length || 0} instances` : instancesResult.error
  );
}

async function testBPMNViewer() {
  console.log('\n🖥️  Testing BPMN Viewer Frontend...');
  
  // Test if BPMN viewer is accessible
  const viewerResult = await makeRequest(BPMN_VIEWER_URL);
  logTest(
    'BPMN Viewer Accessibility',
    viewerResult.success && viewerResult.status === 200,
    viewerResult.success ? 'Frontend is accessible' : viewerResult.error
  );
}

async function testBPMNXMLWithActiveTasks(definitions) {
  console.log('\n📋 Testing BPMN XML with Active Tasks...');
  
  if (!definitions || definitions.length === 0) {
    logTest('BPMN XML with Active Tasks', false, 'No process definitions available for testing');
    return;
  }

  // Test BPMN XML retrieval for first definition
  const firstDefinition = definitions[0];
  const xmlResult = await makeRequest(`${WORKFLOW_API_BASE}/bpmn/unified/definition/${firstDefinition.id}/xml-with-tasks`);
  logTest(
    'BPMN XML Retrieval',
    xmlResult.success && xmlResult.data?.success && xmlResult.data?.bpmnXml,
    xmlResult.success ? 'BPMN XML retrieved successfully' : xmlResult.error
  );

  // Test BPMN XML with process instance (if available)
  if (xmlResult.success && xmlResult.data?.success) {
    const xmlWithInstanceResult = await makeRequest(`${WORKFLOW_API_BASE}/bpmn/unified/definition/${firstDefinition.id}/xml-with-tasks?processInstanceId=test-instance`);
    logTest(
      'BPMN XML with Instance Parameter',
      xmlWithInstanceResult.success && xmlWithInstanceResult.data?.success,
      xmlWithInstanceResult.success ? 'BPMN XML with instance parameter works' : xmlWithInstanceResult.error
    );
  }
}

async function testProcessInstanceStart(definitions) {
  console.log('\n🚀 Testing Process Instance Start...');
  
  if (!definitions || definitions.length === 0) {
    logTest('Process Instance Start', false, 'No process definitions available for testing');
    return;
  }

  // Try to start a process instance
  const firstDefinition = definitions[0];
  const processKey = firstDefinition.key || firstDefinition.id;
  
  const startResult = await makeRequest(`${WORKFLOW_API_BASE}/bpmn/unified/start-instance/${processKey}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: { testVariable: 'integration-test' }
  });
  
  logTest(
    'Process Instance Start',
    startResult.success && startResult.data?.success,
    startResult.success ? `Started instance: ${startResult.data?.data?.id || 'unknown'}` : startResult.error
  );

  return startResult.data?.data;
}

// Main test execution
async function runIntegrationTests() {
  console.log('🧪 Starting BPMN Workflow Integration Tests...');
  console.log('=' .repeat(60));

  try {
    // Test all components
    const definitions = await testWorkflowIntegrationAPI();
    await testFlowableRESTAPI();
    await testBPMNViewer();
    await testBPMNXMLWithActiveTasks(definitions);
    await testProcessInstanceStart(definitions);

    // Print summary
    console.log('\n' + '=' .repeat(60));
    console.log('📊 Test Summary:');
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);

    if (results.failed === 0) {
      console.log('\n🎉 All tests passed! The BPMN Workflow Integration is working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check the logs above for details.');
    }

    // Exit with appropriate code
    process.exit(results.failed === 0 ? 0 : 1);

  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
    process.exit(1);
  }
}

// Handle command line execution
if (require.main === module) {
  runIntegrationTests();
}

module.exports = { runIntegrationTests };

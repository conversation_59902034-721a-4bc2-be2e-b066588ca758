#!/bin/bash

# Test script to verify the complete BPMN workflow flow
echo "🧪 Testing BPMN Workflow System Flow"
echo "===================================="

# Base URLs
WORKFLOW_API="http://localhost:8080/api/v1/workflow"
FRONTEND_URL="http://localhost:3001"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test API endpoint
test_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    echo -e "${BLUE}Testing:${NC} $description"
    echo -e "${YELLOW}URL:${NC} $url"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ "$http_code" -eq "$expected_status" ]; then
        echo -e "${GREEN}✅ SUCCESS${NC} (HTTP $http_code)"
        echo "Response: $(echo $body | jq . 2>/dev/null || echo $body | head -c 200)..."
    else
        echo -e "${RED}❌ FAILED${NC} (HTTP $http_code)"
        echo "Response: $body"
    fi
    echo ""
}

# Function to start a test process
start_test_process() {
    local process_key=$1
    local description=$2
    
    echo -e "${BLUE}Starting Test Process:${NC} $description"
    
    response=$(curl -s -X POST -w "HTTPSTATUS:%{http_code}" "$WORKFLOW_API/bpmn/test/start-process/$process_key")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        echo -e "${GREEN}✅ Process Started Successfully${NC}"
        process_id=$(echo $body | jq -r '.processInstance.id' 2>/dev/null)
        if [ "$process_id" != "null" ] && [ "$process_id" != "" ]; then
            echo -e "${GREEN}Process Instance ID:${NC} $process_id"
            return 0
        fi
    else
        echo -e "${RED}❌ Failed to start process${NC} (HTTP $http_code)"
        echo "Response: $body"
        return 1
    fi
    echo ""
}

echo "🔍 Step 1: Testing API Connectivity"
echo "-----------------------------------"

# Test basic connectivity
test_endpoint "$WORKFLOW_API/bpmn/definitions" "Get Process Definitions"
test_endpoint "$WORKFLOW_API/bpmn/instances" "Get Active Process Instances"
test_endpoint "$WORKFLOW_API/bpmn/instances/history" "Get Historic Process Instances"
test_endpoint "$WORKFLOW_API/bpmn/test/available-processes" "Get Available Processes for Testing"

echo "🚀 Step 2: Starting Test Processes"
echo "----------------------------------"

# Start test processes
start_test_process "Hrdc_workflow" "HRDC Workflow"
start_test_process "Complaint_Workflow" "Complaint Workflow"
start_test_process "Appeal_Workflow" "Appeal Workflow"

echo "📊 Step 3: Verifying Active Instances"
echo "------------------------------------"

# Check active instances after starting processes
test_endpoint "$WORKFLOW_API/bpmn/instances" "Get Active Instances (After Starting Processes)"

echo "🌐 Step 4: Testing Frontend Connectivity"
echo "---------------------------------------"

# Test frontend
if curl -s "$FRONTEND_URL" > /dev/null; then
    echo -e "${GREEN}✅ Frontend is accessible${NC} at $FRONTEND_URL"
else
    echo -e "${RED}❌ Frontend is not accessible${NC} at $FRONTEND_URL"
fi

echo ""
echo "🎯 Step 5: Manual Testing Instructions"
echo "------------------------------------"
echo "1. Open your browser and go to: $FRONTEND_URL"
echo "2. You should see the Workflow Management Dashboard"
echo "3. Check the stats cards for:"
echo "   - Process Definitions count"
echo "   - Active Instances count (should be > 0 after running tests)"
echo "4. Click on 'Active Instances' tab to see running processes"
echo "5. Click 'View BPMN' on any process definition"
echo "6. Select an active instance from the sidebar"
echo "7. Verify that active tasks are highlighted in blue with pulse animation"
echo ""
echo "🧪 Step 6: Additional Test Commands"
echo "---------------------------------"
echo "# Test starting a specific workflow:"
echo "curl -X POST '$WORKFLOW_API/bpmn/test/start-process/Hrdc_workflow'"
echo ""
echo "# Get status of a specific process instance:"
echo "curl '$WORKFLOW_API/bpmn/instance/{INSTANCE_ID}/status'"
echo ""
echo "# Start HRDC workflow via original endpoint:"
echo "curl '$WORKFLOW_API/start-process/HRDC/TEST123'"
echo ""

echo "📋 Step 7: Troubleshooting"
echo "-------------------------"
echo "If you don't see active instances:"
echo "1. Check Docker containers are running: docker-compose ps"
echo "2. Check logs: docker-compose logs workflow-integration"
echo "3. Verify database connection: docker-compose logs postgres-workflow"
echo "4. Check frontend logs: docker-compose logs workflow-bpmn-viewer"
echo ""
echo "If BPMN diagrams don't load:"
echo "1. Check browser console for errors"
echo "2. Verify API endpoints are accessible"
echo "3. Check CORS configuration"
echo ""

echo -e "${GREEN}🎉 Test completed!${NC}"
echo "Open $FRONTEND_URL to see your BPMN workflow system in action!"

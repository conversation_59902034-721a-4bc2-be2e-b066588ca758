version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: workflow-postgres
    environment:
      POSTGRES_DB: workflow
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: admin
      POSTGRES_MULTIPLE_DATABASES: workplace_learning
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - workflow-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Flowable REST API
  flowable-rest:
    image: flowable/flowable-rest:6.8.1
    container_name: flowable-rest-api
    environment:
      - FLOWABLE_DATABASE_TYPE=postgres
      - FLOWABLE_DATABASE_HOST=postgres
      - FLOWABLE_DATABASE_PORT=5432
      - FLOWABLE_DATABASE_NAME=workflow
      - FLOWABLE_DATABASE_USERNAME=postgres
      - FLOWABLE_DATABASE_PASSWORD=admin
      - FLOWABLE_DATABASE_SCHEMA=public
      - FLOWABLE_DATABASE_SCHEMA_UPDATE=true
      - FLOWABLE_REST_APP_ADMIN_USER_ID=rest-admin
      - FLOWABLE_REST_APP_ADMIN_PASSWORD=test
      - FLOWABLE_REST_APP_ADMIN_FIRST_NAME=Rest
      - FLOWABLE_REST_APP_ADMIN_LAST_NAME=Admin
      - FLOWABLE_REST_APP_ADMIN_EMAIL=<EMAIL>
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - workflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/flowable-rest/service/management/engine"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Workflow Integration Service
  workflow-integration:
    build:
      context: ./workflow-integration
      dockerfile: Dockerfile
    container_name: workflow-integration-service
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=workflow
      - DB_USERNAME=postgres
      - DB_PASSWORD=admin
      - FLOWABLE_REST_URL=http://flowable-rest:8080/flowable-rest
      - FLOWABLE_REST_USERNAME=rest-admin
      - FLOWABLE_REST_PASSWORD=test
    ports:
      - "8082:8082"
    depends_on:
      postgres:
        condition: service_healthy
      flowable-rest:
        condition: service_healthy
    networks:
      - workflow-network
    volumes:
      - ./workflow-integration/src/main/resources/processes:/app/processes

  # Workplace Learning Service
  workplace-learning:
    build:
      context: ./workplace-learning
      dockerfile: Dockerfile
    container_name: workplace-learning-service
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=workplace_learning
      - DB_USERNAME=postgres
      - DB_PASSWORD=admin
    ports:
      - "8091:8091"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - workflow-network

  # BPMN Viewer Frontend
  bpmn-viewer:
    build:
      context: ./workflow-bpmn-viewer
      dockerfile: Dockerfile
    container_name: bpmn-viewer-frontend
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:8082/api/v1/workflow
      - REACT_APP_FLOWABLE_API_URL=http://localhost:8080/flowable-rest
    ports:
      - "3001:3001"
    depends_on:
      - workflow-integration
    networks:
      - workflow-network

volumes:
  postgres_data:

networks:
  workflow-network:
    driver: bridge

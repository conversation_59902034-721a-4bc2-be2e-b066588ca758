version: '3.8'

services:
  # PostgreSQL Database for Flowable engine persistence
  postgres:
    image: postgres:15-alpine
    container_name: postgres-flowable
    environment:
      POSTGRES_DB: flowable
      POSTGRES_USER: flowable
      POSTGRES_PASSWORD: flowable
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - flowable-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U flowable -d flowable"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Spring Boot Backend - Serves Flowable engine & custom REST API
  backend:
    build:
      context: ./workflow-integration
      dockerfile: Dockerfile
    container_name: flowable-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_DATASOURCE_USERNAME: flowable
      SPRING_DATASOURCE_PASSWORD: flowable
      SPRING_DATASOURCE_DRIVER_CLASS_NAME: org.postgresql.Driver
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      FLOWABLE_DATABASE_SCHEMA_UPDATE: true
      FLOWABLE_ASYNC_EXECUTOR_ACTIVATE: true
      SERVER_PORT: 8080
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - flowable-network
    volumes:
      - ./workflow-integration/src/main/resources/processes:/app/processes
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # React Frontend - Lists processes, renders BPMN diagrams, manages instances
  frontend:
    build:
      context: ./workflow-bpmn-viewer
      dockerfile: Dockerfile
    container_name: flowable-frontend
    environment:
      VITE_API_BASE_URL: http://localhost:8080/api
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - flowable-network

networks:
  flowable-network:
    driver: bridge
    name: flowable-network

volumes:
  postgres_data:
    driver: local
    name: flowable_postgres_data

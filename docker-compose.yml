version: '3.8'

services:
  # PostgreSQL Database for Workflow
  postgres-workflow:
    image: postgres:15-alpine
    container_name: postgres-workflow
    environment:
      POSTGRES_DB: workflow
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: admin
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_workflow_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - workflow-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d workflow"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Workflow Integration Service (Flowable REST API)
  workflow-integration:
    build:
      context: ./workflow-integration
      dockerfile: Dockerfile
    container_name: workflow-integration
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *************************************************
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: admin
      SPRING_DATASOURCE_DRIVER_CLASS_NAME: org.postgresql.Driver
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      SPRING_JPA_SHOW_SQL: true
      FLOWABLE_DATABASE_SCHEMA_UPDATE: true
      FLOWABLE_ASYNC_EXECUTOR_ACTIVATE: true
      FLOWABLE_REST_APP_ENABLED: true
      SERVER_PORT: 8082
    ports:
      - "8080:8082"
    depends_on:
      postgres-workflow:
        condition: service_healthy
    networks:
      - workflow-network
    volumes:
      - ./workflow-integration/src/main/resources/processes:/app/processes
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Workplace Learning Service
  workplace-learning:
    build:
      context: ./workplace-learning
      dockerfile: Dockerfile
    container_name: workplace-learning
    environment:
      SPRING_PROFILES_ACTIVE: docker
      WORKFLOW_SERVICE_URL: http://workflow-integration:8080
      SERVER_PORT: 8091
    ports:
      - "8091:8091"
    depends_on:
      workflow-integration:
        condition: service_healthy
    networks:
      - workflow-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8091/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # BPMN Viewer Frontend
  workflow-bpmn-viewer:
    build:
      context: ./workflow-bpmn-viewer
      dockerfile: Dockerfile
    container_name: workflow-bpmn-viewer
    environment:
      VITE_API_BASE_URL: http://localhost:8080/api/v1/workflow
      VITE_WORKFLOW_SERVICE_URL: http://workflow-integration:8082
    ports:
      - "3001:3001"
    depends_on:
      workflow-integration:
        condition: service_healthy
    networks:
      - workflow-network

networks:
  workflow-network:
    driver: bridge
    name: workflow-network

volumes:
  postgres_workflow_data:
    driver: local
    name: postgres_workflow_data

export interface ProcessDefinition {
  id: string;
  key: string;
  name: string;
  version: number;
  deploymentId: string;
  resourceName: string;
  category: string;
}

export interface ProcessInstance {
  id: string;
  processDefinitionId: string;
  processDefinitionKey: string;
  processDefinitionName: string;
  startTime: string;
  endTime?: string;
  durationInMillis?: number;
  startUserId?: string;
  businessKey?: string;
  suspended?: boolean;
  status: 'ACTIVE' | 'COMPLETED' | 'SUSPENDED';
  currentActivities: string[];
  variables?: Record<string, any>;
  deleteReason?: string;
}

export interface WorkflowApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

export interface ProcessInstanceStatus {
  status: 'ACTIVE' | 'COMPLETED' | 'SUSPENDED';
  processInstance: ProcessInstance;
  activeTasks: Task[];
  activeTaskCount: number;
}

export interface Task {
  id: string;
  name: string;
  description?: string;
  assignee?: string;
  owner?: string;
  createTime: string;
  dueDate?: string;
  priority: number;
  processInstanceId: string;
  executionId: string;
  processDefinitionId: string;
  taskDefinitionKey: string;
  suspended: boolean;
  tenantId?: string;
}
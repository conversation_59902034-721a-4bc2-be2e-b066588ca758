package com.workflowenginee.workflow.controller;

import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/workflow/bpmn")
@CrossOrigin(origins = "*")
@Slf4j
public class BpmnResourceController {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private TaskService taskService;

    /**
     * Get all process definitions from both deployed processes and resource files
     */
    @GetMapping("/definitions")
    public ResponseEntity<?> getProcessDefinitions() {
        try {
            System.out.println("Fetching all process definitions");
            
            // Get deployed process definitions from Flowable
            List<ProcessDefinition> deployedDefinitions = repositoryService.createProcessDefinitionQuery()
                    .latestVersion()
                    .list();

            // Get BPMN files from resources/processes folder
            List<Map<String, Object>> resourceDefinitions = getBpmnResourceFiles();

            // Combine both sources
            List<Map<String, Object>> allDefinitions = new ArrayList<>();
            
            // Add deployed definitions
            for (ProcessDefinition def : deployedDefinitions) {
                Map<String, Object> definition = new HashMap<>();
                definition.put("id", def.getId());
                definition.put("key", def.getKey());
                definition.put("name", def.getName() != null ? def.getName() : def.getKey());
                definition.put("version", def.getVersion());
                definition.put("deploymentId", def.getDeploymentId());
                definition.put("category", def.getCategory());
                definition.put("description", def.getDescription());
                definition.put("suspended", def.isSuspended());
                definition.put("tenantId", def.getTenantId());
                definition.put("source", "deployed");
                allDefinitions.add(definition);
            }
            
            // Add resource definitions (not yet deployed)
            allDefinitions.addAll(resourceDefinitions);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", allDefinitions);
            response.put("total", allDefinitions.size());
            response.put("deployed", deployedDefinitions.size());
            response.put("resources", resourceDefinitions.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("Error fetching process definitions: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching process definitions: " + e.getMessage()
            ));
        }
    }

    /**
     * Get BPMN XML for a specific process definition
     */
    @GetMapping("/definition/{processDefinitionId}/xml")
    public ResponseEntity<?> getProcessDefinitionXml(@PathVariable String processDefinitionId) {
        try {
            System.out.println("Fetching BPMN XML for process definition: " + processDefinitionId);

            String bpmnXml = null;

            // First try to get from deployed processes
            try {
                ProcessDefinition processDefinition = repositoryService.getProcessDefinition(processDefinitionId);
                if (processDefinition != null) {
                    InputStream xmlStream = repositoryService.getResourceAsStream(
                        processDefinition.getDeploymentId(), 
                        processDefinition.getResourceName()
                    );
                    bpmnXml = new String(xmlStream.readAllBytes(), StandardCharsets.UTF_8);
                    System.out.println("Found BPMN XML from deployed process");
                }
            } catch (Exception e) {
                System.out.println("Process definition not found in deployed processes: " + processDefinitionId);
            }

            // If not found in deployed processes, try resource files
            if (bpmnXml == null) {
                bpmnXml = getBpmnXmlFromResources(processDefinitionId);
            }

            if (bpmnXml != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_XML);
                return ResponseEntity.ok()
                    .headers(headers)
                    .body(bpmnXml);
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                    "success", false,
                    "message", "BPMN XML not found for process definition: " + processDefinitionId
                ));
            }

        } catch (Exception e) {
            System.err.println("Error fetching BPMN XML for process definition: " + processDefinitionId + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching BPMN XML: " + e.getMessage()
            ));
        }
    }

    /**
     * Get BPMN XML for a specific process definition (alternative endpoint)
     */
    @GetMapping("/definitions/{processDefinitionId}/xml")
    public ResponseEntity<?> getProcessDefinitionXmlAlternative(@PathVariable String processDefinitionId) {
        return getProcessDefinitionXml(processDefinitionId);
    }

    /**
     * Get all BPMN resource files from the processes folder
     */
    @GetMapping("/resources")
    public ResponseEntity<?> getBpmnResources() {
        try {
            List<Map<String, Object>> resourceFiles = getBpmnResourceFiles();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", resourceFiles,
                "total", resourceFiles.size()
            ));

        } catch (Exception e) {
            System.err.println("Error fetching BPMN resource files: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching BPMN resource files: " + e.getMessage()
            ));
        }
    }

    /**
     * Deploy a BPMN resource file to Flowable engine
     */
    @PostMapping("/deploy-resource/{fileName}")
    public ResponseEntity<?> deployBpmnResource(@PathVariable String fileName) {
        try {
            System.out.println("Deploying BPMN resource file: " + fileName);

            String resourcePath = "processes/" + fileName;
            Resource resource = new ClassPathResource(resourcePath);

            if (resource.exists()) {
                try (InputStream inputStream = resource.getInputStream()) {
                    String bpmnXml = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);

                    // Deploy to Flowable
                    org.flowable.engine.repository.Deployment deployment = repositoryService.createDeployment()
                        .name("Deployment of " + fileName)
                        .addString(fileName, bpmnXml)
                        .deploy();

                    // Get the deployed process definition
                    ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                        .deploymentId(deployment.getId())
                        .singleResult();

                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "BPMN resource deployed successfully");
                    response.put("deploymentId", deployment.getId());
                    response.put("processDefinitionId", processDefinition.getId());
                    response.put("processDefinitionKey", processDefinition.getKey());
                    response.put("fileName", fileName);

                    System.out.println("Successfully deployed BPMN resource: " + fileName + " with deployment ID: " + deployment.getId());
                    return ResponseEntity.ok(response);
                }
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                    "success", false,
                    "message", "BPMN resource file not found: " + fileName
                ));
            }

        } catch (Exception e) {
            System.err.println("Error deploying BPMN resource file: " + fileName + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error deploying BPMN resource: " + e.getMessage()
            ));
        }
    }

    /**
     * Get BPMN XML content from a resource file
     */
    @GetMapping("/resource/{fileName}/xml")
    public ResponseEntity<?> getBpmnResourceXml(@PathVariable String fileName) {
        try {
            System.out.println("Fetching BPMN XML from resource file: " + fileName);
            
            String resourcePath = "processes/" + fileName;
            Resource resource = new ClassPathResource(resourcePath);
            
            if (resource.exists()) {
                try (InputStream inputStream = resource.getInputStream()) {
                    String bpmnXml = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
                    
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_XML);
                    return ResponseEntity.ok()
                        .headers(headers)
                        .body(bpmnXml);
                }
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                    "success", false,
                    "message", "BPMN resource file not found: " + fileName
                ));
            }

        } catch (Exception e) {
            System.err.println("Error fetching BPMN XML from resource file: " + fileName + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching BPMN XML from resource: " + e.getMessage()
            ));
        }
    }

    /**
     * Start a new process instance
     */
    @PostMapping("/start-instance/{processDefinitionKey}")
    public ResponseEntity<?> startProcessInstance(
            @PathVariable String processDefinitionKey,
            @RequestBody(required = false) Map<String, Object> variables) {
        try {
            System.out.println("Starting process instance for: " + processDefinitionKey);

            if (variables == null) {
                variables = new HashMap<>();
            }

            // Add some default variables for your workflows
            if ("Hrdc_workflow".equals(processDefinitionKey)) {
                variables.putIfAbsent("applicationNumber", "APP" + System.currentTimeMillis());
                variables.putIfAbsent("applicationType", "HRDC");
                variables.putIfAbsent("role", "Agent_lead");
            } else if ("Complaint_Workflow".equals(processDefinitionKey)) {
                variables.putIfAbsent("complaintNumber", "COMP" + System.currentTimeMillis());
                variables.putIfAbsent("complaintType", "Service");
            } else if ("Appeal_Workflow".equals(processDefinitionKey)) {
                variables.putIfAbsent("appealNumber", "APP" + System.currentTimeMillis());
                variables.putIfAbsent("appealType", "Decision Review");
            }

            org.flowable.engine.runtime.ProcessInstance processInstance = runtimeService
                .startProcessInstanceByKey(processDefinitionKey, variables);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Process instance started successfully");
            response.put("processInstanceId", processInstance.getId());
            response.put("processDefinitionKey", processDefinitionKey);
            response.put("businessKey", processInstance.getBusinessKey());
            response.put("variables", variables);

            System.out.println("Successfully started process instance: " + processInstance.getId());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("Error starting process instance: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error starting process instance: " + e.getMessage()
            ));
        }
    }









    /**
     * Helper method to get BPMN files from resources/processes folder
     */
    private List<Map<String, Object>> getBpmnResourceFiles() {
        List<Map<String, Object>> resourceFiles = new ArrayList<>();
        
        try {
            // List of known BPMN files in resources/processes
            String[] bpmnFiles = {
                "check-demo.bpmn20.xml",
                "check-demo1.bpmn20.xml", 
                "Appeal_Workflow.bpmn20.xml"
            };
            
            for (String fileName : bpmnFiles) {
                String resourcePath = "processes/" + fileName;
                Resource resource = new ClassPathResource(resourcePath);
                
                if (resource.exists()) {
                    try (InputStream inputStream = resource.getInputStream()) {
                        String content = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
                        
                        // Extract process information from BPMN XML
                        Map<String, Object> fileInfo = extractProcessInfoFromXml(content, fileName);
                        if (fileInfo != null) {
                            fileInfo.put("source", "resource");
                            fileInfo.put("fileName", fileName);
                            fileInfo.put("resourcePath", resourcePath);
                            resourceFiles.add(fileInfo);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("Error reading BPMN resource files: " + e.getMessage());
        }
        
        return resourceFiles;
    }

    /**
     * Helper method to get BPMN XML from resources by process definition ID or key
     */
    private String getBpmnXmlFromResources(String processDefinitionId) {
        try {
            String[] bpmnFiles = {
                "check-demo.bpmn20.xml",
                "check-demo1.bpmn20.xml",
                "Appeal_Workflow.bpmn20.xml"
            };

            for (String fileName : bpmnFiles) {
                String resourcePath = "processes/" + fileName;
                Resource resource = new ClassPathResource(resourcePath);

                if (resource.exists()) {
                    try (InputStream inputStream = resource.getInputStream()) {
                        String content = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);

                        // Check if this file contains the requested process
                        // Match by process definition ID, filename, or extracted process key
                        String fileNameWithoutExtension = fileName.replace(".bpmn20.xml", "");
                        if (content.contains("id=\"" + processDefinitionId + "\"") ||
                            fileName.equals(processDefinitionId) ||
                            fileNameWithoutExtension.equals(processDefinitionId) ||
                            processDefinitionId.contains(fileNameWithoutExtension)) {
                            System.out.println("Found matching BPMN XML in resource file: " + fileName);
                            return content;
                        }

                        // Also try to match by process key extracted from XML
                        String processKey = extractXmlAttribute(content, "process", "id");
                        if (processKey != null && processKey.equals(processDefinitionId)) {
                            System.out.println("Found matching BPMN XML by process key: " + processKey + " in file: " + fileName);
                            return content;
                        }
                    }
                }
            }

            System.out.println("No matching BPMN XML found in resources for process definition: " + processDefinitionId);

        } catch (Exception e) {
            System.err.println("Error reading BPMN XML from resources: " + e.getMessage());
        }

        return null;
    }

    /**
     * Helper method to extract process information from BPMN XML
     */
    private Map<String, Object> extractProcessInfoFromXml(String xmlContent, String fileName) {
        try {
            Map<String, Object> processInfo = new HashMap<>();
            
            // Extract process ID
            String processId = extractXmlAttribute(xmlContent, "process", "id");
            String processName = extractXmlAttribute(xmlContent, "process", "name");
            
            if (processId != null) {
                processInfo.put("id", fileName.replace(".bpmn20.xml", "")); // Use filename as ID for resources
                processInfo.put("key", processId);
                processInfo.put("name", processName != null ? processName : processId);
                processInfo.put("version", 1);
                processInfo.put("deploymentId", null);
                processInfo.put("category", null);
                processInfo.put("description", "BPMN process from resources folder");
                processInfo.put("suspended", false);
                processInfo.put("tenantId", null);
                
                return processInfo;
            }
            
        } catch (Exception e) {
            System.err.println("Error extracting process info from XML: " + fileName + " - " + e.getMessage());
        }
        
        return null;
    }

    /**
     * Helper method to extract XML attributes
     */
    private String extractXmlAttribute(String xmlContent, String elementName, String attributeName) {
        try {
            String pattern = "<" + elementName + "\\s+[^>]*" + attributeName + "=\"([^\"]+)\"";
            java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher matcher = regex.matcher(xmlContent);
            
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            System.out.println("Error extracting XML attribute: " + attributeName + " from element: " + elementName);
        }
        
        return null;
    }

    /**
     * Get all active process instances
     */
    @GetMapping("/instances")
    @CrossOrigin(origins = "*")
    public ResponseEntity<?> getProcessInstances() {
        try {
            log.info("Fetching all active process instances");

            List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery()
                    .active()
                    .list();

            List<Map<String, Object>> instanceData = processInstances.stream()
                    .map(this::convertProcessInstanceToMap)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", instanceData,
                "total", instanceData.size()
            ));

        } catch (Exception e) {
            log.error("Error fetching process instances: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching process instances: " + e.getMessage()
            ));
        }
    }

    /**
     * Get historic process instances
     */
    @GetMapping("/instances/history")
    @CrossOrigin(origins = "*")
    public ResponseEntity<?> getHistoricProcessInstances() {
        try {
            log.info("Fetching historic process instances");

            List<HistoricProcessInstance> historicInstances = historyService.createHistoricProcessInstanceQuery()
                    .finished()
                    .orderByProcessInstanceEndTime()
                    .desc()
                    .listPage(0, 100); // Limit to last 100 instances

            List<Map<String, Object>> instanceData = historicInstances.stream()
                    .map(this::convertHistoricProcessInstanceToMap)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", instanceData,
                "total", instanceData.size()
            ));

        } catch (Exception e) {
            log.error("Error fetching historic process instances: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching historic process instances: " + e.getMessage()
            ));
        }
    }

    /**
     * Get process instance status and active tasks
     */
    @GetMapping("/instance/{processInstanceId}/status")
    @CrossOrigin(origins = "*")
    public ResponseEntity<?> getProcessInstanceStatus(@PathVariable String processInstanceId) {
        try {
            log.info("Fetching status for process instance: {}", processInstanceId);

            // Check if instance is still active
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            Map<String, Object> statusData = new HashMap<>();

            if (processInstance != null) {
                // Active instance
                statusData.put("status", "ACTIVE");
                statusData.put("processInstance", convertProcessInstanceToMap(processInstance));

                // Get active tasks
                List<Task> activeTasks = taskService.createTaskQuery()
                        .processInstanceId(processInstanceId)
                        .active()
                        .list();

                List<Map<String, Object>> taskData = activeTasks.stream()
                        .map(this::convertTaskToMap)
                        .collect(Collectors.toList());

                statusData.put("activeTasks", taskData);
                statusData.put("activeTaskCount", taskData.size());

            } else {
                // Check historic instances
                HistoricProcessInstance historicInstance = historyService.createHistoricProcessInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .singleResult();

                if (historicInstance != null) {
                    statusData.put("status", "COMPLETED");
                    statusData.put("processInstance", convertHistoricProcessInstanceToMap(historicInstance));
                    statusData.put("activeTasks", Collections.emptyList());
                    statusData.put("activeTaskCount", 0);
                } else {
                    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                        "success", false,
                        "message", "Process instance not found: " + processInstanceId
                    ));
                }
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", statusData
            ));

        } catch (Exception e) {
            log.error("Error fetching process instance status: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching process instance status: " + e.getMessage()
            ));
        }
    }

    /**
     * Helper method to convert ProcessInstance to Map
     */
    private Map<String, Object> convertProcessInstanceToMap(ProcessInstance processInstance) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", processInstance.getId());
        data.put("processDefinitionId", processInstance.getProcessDefinitionId());
        data.put("processDefinitionKey", processInstance.getProcessDefinitionKey());
        data.put("processDefinitionName", processInstance.getProcessDefinitionName());
        data.put("processDefinitionVersion", processInstance.getProcessDefinitionVersion());
        data.put("businessKey", processInstance.getBusinessKey());
        data.put("startTime", processInstance.getStartTime());
        data.put("startUserId", processInstance.getStartUserId());
        data.put("suspended", processInstance.isSuspended());
        data.put("tenantId", processInstance.getTenantId());
        return data;
    }

    /**
     * Helper method to convert HistoricProcessInstance to Map
     */
    private Map<String, Object> convertHistoricProcessInstanceToMap(HistoricProcessInstance historicInstance) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", historicInstance.getId());
        data.put("processDefinitionId", historicInstance.getProcessDefinitionId());
        data.put("processDefinitionKey", historicInstance.getProcessDefinitionKey());
        data.put("processDefinitionName", historicInstance.getProcessDefinitionName());
        data.put("processDefinitionVersion", historicInstance.getProcessDefinitionVersion());
        data.put("businessKey", historicInstance.getBusinessKey());
        data.put("startTime", historicInstance.getStartTime());
        data.put("endTime", historicInstance.getEndTime());
        data.put("durationInMillis", historicInstance.getDurationInMillis());
        data.put("startUserId", historicInstance.getStartUserId());
        data.put("deleteReason", historicInstance.getDeleteReason());
        data.put("tenantId", historicInstance.getTenantId());
        return data;
    }

    /**
     * Helper method to convert Task to Map
     */
    private Map<String, Object> convertTaskToMap(Task task) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", task.getId());
        data.put("name", task.getName());
        data.put("description", task.getDescription());
        data.put("assignee", task.getAssignee());
        data.put("owner", task.getOwner());
        data.put("createTime", task.getCreateTime());
        data.put("dueDate", task.getDueDate());
        data.put("priority", task.getPriority());
        data.put("processInstanceId", task.getProcessInstanceId());
        data.put("executionId", task.getExecutionId());
        data.put("processDefinitionId", task.getProcessDefinitionId());
        data.put("taskDefinitionKey", task.getTaskDefinitionKey());
        data.put("suspended", task.isSuspended());
        data.put("tenantId", task.getTenantId());
        return data;
    }

    /**
     * Test endpoint to start a sample process for testing
     */
    @PostMapping("/test/start-process/{processKey}")
    @CrossOrigin(origins = "*")
    public ResponseEntity<?> startTestProcess(@PathVariable String processKey) {
        try {
            log.info("Starting test process with key: {}", processKey);

            // Prepare test variables
            Map<String, Object> variables = new HashMap<>();
            variables.put("testMode", true);
            variables.put("startTime", System.currentTimeMillis());

            // Add specific variables based on process key
            switch (processKey) {
                case "Hrdc_workflow":
                    variables.put("applicationNumber", "TEST-APP-" + System.currentTimeMillis());
                    variables.put("applicationType", "HRDC_TEST");
                    variables.put("role", "Agent_lead");
                    break;
                case "Complaint_Workflow":
                    variables.put("complaintId", "TEST-COMP-" + System.currentTimeMillis());
                    variables.put("role", "AGENT");
                    variables.put("agentMessage", "Test complaint for BPMN viewer");
                    break;
                case "Appeal_Workflow":
                    variables.put("appealId", "TEST-APPEAL-" + System.currentTimeMillis());
                    variables.put("role", "MANAGER");
                    variables.put("managerMessage", "Test appeal for BPMN viewer");
                    break;
                default:
                    variables.put("testId", "TEST-" + System.currentTimeMillis());
            }

            // Start the process
            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processKey, variables);

            log.info("Test process started successfully: {}", processInstance.getId());

            // Get process instance details
            Map<String, Object> instanceData = convertProcessInstanceToMap(processInstance);

            // Get active tasks
            List<Task> activeTasks = taskService.createTaskQuery()
                    .processInstanceId(processInstance.getId())
                    .active()
                    .list();

            List<Map<String, Object>> taskData = activeTasks.stream()
                    .map(this::convertTaskToMap)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Test process started successfully",
                "processInstance", instanceData,
                "activeTasks", taskData,
                "activeTaskCount", taskData.size()
            ));

        } catch (Exception e) {
            log.error("Error starting test process: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error starting test process: " + e.getMessage()
            ));
        }
    }

    /**
     * Get all available process keys for testing
     */
    @GetMapping("/test/available-processes")
    @CrossOrigin(origins = "*")
    public ResponseEntity<?> getAvailableProcesses() {
        try {
            List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
                    .latestVersion()
                    .list();

            List<Map<String, Object>> availableProcesses = processDefinitions.stream()
                    .map(pd -> {
                        Map<String, Object> processInfo = new HashMap<>();
                        processInfo.put("key", pd.getKey());
                        processInfo.put("name", pd.getName());
                        processInfo.put("version", pd.getVersion());
                        processInfo.put("id", pd.getId());
                        return processInfo;
                    })
                    .collect(Collectors.toList());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", availableProcesses,
                "total", availableProcesses.size()
            ));

        } catch (Exception e) {
            log.error("Error getting available processes: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error getting available processes: " + e.getMessage()
            ));
        }
    }
}

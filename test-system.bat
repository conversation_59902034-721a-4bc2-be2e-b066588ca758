@echo off
echo 🧪 Testing BPMN Workflow System
echo ===============================

REM Test URLs
set BACKEND_API=http://localhost:8080/api
set FRONTEND_URL=http://localhost:3000

echo 📋 Step 1: Testing Backend API
echo ------------------------------

REM Test process definitions
echo Testing: GET %BACKEND_API%/definitions
curl -s "%BACKEND_API%/definitions"
echo.

REM Test BPMN XML retrieval
echo Testing: GET %BACKEND_API%/definition/check-demo/xml
curl -s "%BACKEND_API%/definition/check-demo/xml" >nul 2>&1
if errorlevel 1 (
    echo ❌ Failed to retrieve BPMN XML
) else (
    echo ✅ BPMN XML retrieved successfully
)
echo.

echo 🚀 Step 2: Starting Test Process
echo --------------------------------

REM Start a test process
echo Starting HRDC workflow...
curl -X POST -s "%BACKEND_API%/test/start-process/Hrdc_workflow"
echo.

echo 📊 Step 3: Checking Active Instances
echo -----------------------------------

REM Check active instances
echo Getting active instances...
curl -s "%BACKEND_API%/instances"
echo.

echo 🌐 Step 4: Testing Frontend
echo ---------------------------

REM Test frontend accessibility
curl -s "%FRONTEND_URL%" >nul 2>&1
if errorlevel 1 (
    echo ❌ Frontend is not accessible
) else (
    echo ✅ Frontend is accessible at %FRONTEND_URL%
)
echo.

echo 🎯 Step 5: Manual Testing Guide
echo ------------------------------
echo 1. Open browser: %FRONTEND_URL%
echo 2. You should see:
echo    - List of available processes
echo    - Process definition cards
echo    - Test buttons to start processes
echo.
echo 3. Click 'View BPMN' on any process to:
echo    - See interactive BPMN diagram
echo    - View process instances (if any)
echo    - See active task highlighting
echo.
echo 4. Use test buttons to start processes and see them in action
echo.

echo ✅ System test completed!
echo Open %FRONTEND_URL% to interact with your BPMN workflow system.
pause

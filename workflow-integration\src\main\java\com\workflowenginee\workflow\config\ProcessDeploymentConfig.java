package com.workflowenginee.workflow.config;

import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * Configuration class to automatically deploy BPMN processes on application startup
 */
@Component
@Slf4j
public class ProcessDeploymentConfig implements ApplicationRunner {

    @Autowired
    private RepositoryService repositoryService;

    private static final List<String> BPMN_FILES = Arrays.asList(
        "check-demo.bpmn20.xml",
        "check-demo1.bpmn20.xml", 
        "Appeal_Workflow.bpmn20.xml"
    );

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("Starting automatic BPMN process deployment...");
        
        try {
            deployBpmnProcesses();
            logDeployedProcesses();
        } catch (Exception e) {
            log.error("Error during BPMN process deployment: {}", e.getMessage(), e);
        }
    }

    /**
     * Deploy all BPMN processes from the resources/processes folder
     */
    private void deployBpmnProcesses() {
        for (String fileName : BPMN_FILES) {
            try {
                deployBpmnFile(fileName);
            } catch (Exception e) {
                log.error("Failed to deploy BPMN file: {} - {}", fileName, e.getMessage());
            }
        }
    }

    /**
     * Deploy a single BPMN file
     */
    private void deployBpmnFile(String fileName) throws Exception {
        String resourcePath = "processes/" + fileName;
        Resource resource = new ClassPathResource(resourcePath);
        
        if (!resource.exists()) {
            log.warn("BPMN file not found: {}", resourcePath);
            return;
        }

        try (InputStream inputStream = resource.getInputStream()) {
            String bpmnXml = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            
            // Check if process is already deployed
            String processKey = extractProcessKey(bpmnXml);
            if (processKey != null && isProcessAlreadyDeployed(processKey)) {
                log.info("Process '{}' from file '{}' is already deployed, skipping...", processKey, fileName);
                return;
            }

            // Deploy the process
            Deployment deployment = repositoryService.createDeployment()
                .name("Auto-deployment of " + fileName)
                .addString(fileName, bpmnXml)
                .deploy();

            log.info("Successfully deployed BPMN process from file: {} (Deployment ID: {})", 
                fileName, deployment.getId());
                
        } catch (Exception e) {
            log.error("Error deploying BPMN file: {} - {}", fileName, e.getMessage());
            throw e;
        }
    }

    /**
     * Extract process key from BPMN XML
     */
    private String extractProcessKey(String bpmnXml) {
        try {
            String pattern = "<process\\s+[^>]*id=\"([^\"]+)\"";
            java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher matcher = regex.matcher(bpmnXml);
            
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            log.warn("Could not extract process key from BPMN XML: {}", e.getMessage());
        }
        return null;
    }

    /**
     * Check if a process with the given key is already deployed
     */
    private boolean isProcessAlreadyDeployed(String processKey) {
        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(processKey)
                .latestVersion()
                .singleResult();
            return processDefinition != null;
        } catch (Exception e) {
            log.warn("Error checking if process is deployed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Log all currently deployed processes
     */
    private void logDeployedProcesses() {
        try {
            List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
                .latestVersion()
                .list();

            log.info("Currently deployed processes:");
            for (ProcessDefinition pd : processDefinitions) {
                log.info("  - Process Key: '{}', Name: '{}', Version: {}, Deployment ID: {}", 
                    pd.getKey(), pd.getName(), pd.getVersion(), pd.getDeploymentId());
            }
            
            log.info("Total deployed processes: {}", processDefinitions.size());
            
        } catch (Exception e) {
            log.error("Error logging deployed processes: {}", e.getMessage());
        }
    }
}

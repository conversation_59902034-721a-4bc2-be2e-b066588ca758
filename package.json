{"name": "bpmn-workflow-integration", "version": "1.0.0", "description": "BPMN Workflow Integration System with Flowable REST API and React Frontend", "main": "test-integration.js", "scripts": {"test": "node test-integration.js", "start": "docker-compose up -d", "stop": "docker-compose down", "restart": "docker-compose down && docker-compose up -d", "logs": "docker-compose logs -f", "clean": "docker-compose down -v && docker system prune -f"}, "dependencies": {"axios": "^1.6.0"}, "devDependencies": {}, "keywords": ["bpmn", "workflow", "flowable", "docker", "react", "integration"], "author": "Workflow Integration Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/your-org/bpmn-workflow-integration.git"}, "bugs": {"url": "https://github.com/your-org/bpmn-workflow-integration/issues"}, "homepage": "https://github.com/your-org/bpmn-workflow-integration#readme"}
package com.workflowenginee.workflow.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.*;
import java.util.Base64;

@Service
public class FlowableRestService {

    @Value("${flowable-rest.base-url}")
    private String flowableBaseUrl;

    @Value("${flowable-rest.username}")
    private String username;

    @Value("${flowable-rest.password}")
    private String password;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public FlowableRestService() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    private HttpHeaders createAuthHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String auth = username + ":" + password;
        byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
        String authHeader = "Basic " + new String(encodedAuth);
        headers.set("Authorization", authHeader);
        return headers;
    }

    /**
     * Get all process definitions from Flowable REST API
     */
    public List<Map<String, Object>> getProcessDefinitions() {
        try {
            String url = flowableBaseUrl + "/service/repository/process-definitions";
            HttpEntity<String> entity = new HttpEntity<>(createAuthHeaders());
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                JsonNode dataNode = jsonNode.get("data");
                
                List<Map<String, Object>> definitions = new ArrayList<>();
                if (dataNode != null && dataNode.isArray()) {
                    for (JsonNode definition : dataNode) {
                        Map<String, Object> defMap = objectMapper.convertValue(definition, Map.class);
                        definitions.add(defMap);
                    }
                }
                return definitions;
            }
        } catch (Exception e) {
            System.err.println("Error fetching process definitions from Flowable: " + e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * Get process definition XML from Flowable REST API
     */
    public String getProcessDefinitionXml(String processDefinitionId) {
        try {
            String url = flowableBaseUrl + "/service/repository/process-definitions/" + processDefinitionId + "/resourcedata";
            HttpEntity<String> entity = new HttpEntity<>(createAuthHeaders());
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            }
        } catch (Exception e) {
            System.err.println("Error fetching process definition XML from Flowable: " + e.getMessage());
        }
        return null;
    }

    /**
     * Get all process instances from Flowable REST API
     */
    public List<Map<String, Object>> getProcessInstances() {
        try {
            String url = flowableBaseUrl + "/service/runtime/process-instances";
            HttpEntity<String> entity = new HttpEntity<>(createAuthHeaders());
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                JsonNode dataNode = jsonNode.get("data");
                
                List<Map<String, Object>> instances = new ArrayList<>();
                if (dataNode != null && dataNode.isArray()) {
                    for (JsonNode instance : dataNode) {
                        Map<String, Object> instMap = objectMapper.convertValue(instance, Map.class);
                        instances.add(instMap);
                    }
                }
                return instances;
            }
        } catch (Exception e) {
            System.err.println("Error fetching process instances from Flowable: " + e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * Get historic process instances from Flowable REST API
     */
    public List<Map<String, Object>> getHistoricProcessInstances() {
        try {
            String url = flowableBaseUrl + "/service/history/historic-process-instances";
            HttpEntity<String> entity = new HttpEntity<>(createAuthHeaders());
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                JsonNode dataNode = jsonNode.get("data");
                
                List<Map<String, Object>> instances = new ArrayList<>();
                if (dataNode != null && dataNode.isArray()) {
                    for (JsonNode instance : dataNode) {
                        Map<String, Object> instMap = objectMapper.convertValue(instance, Map.class);
                        instances.add(instMap);
                    }
                }
                return instances;
            }
        } catch (Exception e) {
            System.err.println("Error fetching historic process instances from Flowable: " + e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * Get active tasks for a process instance
     */
    public List<Map<String, Object>> getActiveTasks(String processInstanceId) {
        try {
            String url = flowableBaseUrl + "/service/runtime/tasks?processInstanceId=" + processInstanceId;
            HttpEntity<String> entity = new HttpEntity<>(createAuthHeaders());
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                JsonNode dataNode = jsonNode.get("data");
                
                List<Map<String, Object>> tasks = new ArrayList<>();
                if (dataNode != null && dataNode.isArray()) {
                    for (JsonNode task : dataNode) {
                        Map<String, Object> taskMap = objectMapper.convertValue(task, Map.class);
                        tasks.add(taskMap);
                    }
                }
                return tasks;
            }
        } catch (Exception e) {
            System.err.println("Error fetching active tasks from Flowable: " + e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * Start a process instance
     */
    public Map<String, Object> startProcessInstance(String processDefinitionKey, Map<String, Object> variables) {
        try {
            String url = flowableBaseUrl + "/service/runtime/process-instances";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("processDefinitionKey", processDefinitionKey);
            if (variables != null && !variables.isEmpty()) {
                requestBody.put("variables", variables);
            }
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, createAuthHeaders());
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.CREATED) {
                return objectMapper.readValue(response.getBody(), Map.class);
            }
        } catch (Exception e) {
            System.err.println("Error starting process instance in Flowable: " + e.getMessage());
        }
        return null;
    }

    /**
     * Get process instance details with active tasks
     */
    public Map<String, Object> getProcessInstanceWithActiveTasks(String processInstanceId) {
        try {
            // Get process instance details
            String instanceUrl = flowableBaseUrl + "/service/runtime/process-instances/" + processInstanceId;
            HttpEntity<String> entity = new HttpEntity<>(createAuthHeaders());
            
            ResponseEntity<String> instanceResponse = restTemplate.exchange(instanceUrl, HttpMethod.GET, entity, String.class);
            
            if (instanceResponse.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> instance = objectMapper.readValue(instanceResponse.getBody(), Map.class);
                
                // Get active tasks for this instance
                List<Map<String, Object>> activeTasks = getActiveTasks(processInstanceId);
                instance.put("activeTasks", activeTasks);
                
                return instance;
            }
        } catch (Exception e) {
            System.err.println("Error fetching process instance details from Flowable: " + e.getMessage());
        }
        return null;
    }
}

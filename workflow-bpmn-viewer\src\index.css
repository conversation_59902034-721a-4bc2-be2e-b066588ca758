@tailwind base;
@tailwind components;
@tailwind utilities;

/* BPMN.js styles */
@import 'bpmn-js/dist/assets/diagram-js.css';
@import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom BPMN viewer styles */
.bpmn-container {
  height: 100%;
  width: 100%;
}

.djs-palette {
  display: none !important;
}

.djs-context-pad {
  display: none !important;
}

/* Highlight active elements */
.highlight-active {
  stroke: #52c41a !important;
  stroke-width: 3px !important;
  fill: #f6ffed !important;
}

/* Highlight current task */
.highlight-current {
  stroke: #1890ff !important;
  stroke-width: 4px !important;
  fill: #e6f7ff !important;
  animation: pulse-current 2s infinite;
}

/* Highlight completed elements */
.highlight-completed {
  stroke: #52c41a !important;
  stroke-width: 2px !important;
  fill: #f6ffed !important;
  opacity: 0.7;
}

/* Pulse animation for current tasks */
@keyframes pulse-current {
  0% {
    stroke-width: 4px;
    opacity: 1;
  }
  50% {
    stroke-width: 6px;
    opacity: 0.8;
  }
  100% {
    stroke-width: 4px;
    opacity: 1;
  }
}

/* Status indicators */
.status-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-active {
  background-color: #52c41a;
}

.status-completed {
  background-color: #1890ff;
}

.status-suspended {
  background-color: #faad14;
}

.status-error {
  background-color: #ff4d4f;
}
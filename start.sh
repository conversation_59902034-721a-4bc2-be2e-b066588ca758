#!/bin/bash

echo "🚀 Starting BPMN Workflow System"
echo "==============================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

echo "✅ Docker is running"

# Start the system
echo "🏗️ Building and starting services..."
docker-compose up --build -d

echo "⏳ Waiting for services to start..."
sleep 30

# Check if services are running
echo "🔍 Checking service status..."

# Check backend
if curl -f http://localhost:8080/api/definitions > /dev/null 2>&1; then
    echo "✅ Backend API is running at http://localhost:8080/api"
else
    echo "❌ Backend API is not responding"
fi

# Check frontend
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend is running at http://localhost:3000"
else
    echo "❌ Frontend is not responding"
fi

echo ""
echo "🎉 BPMN Workflow System is ready!"
echo "================================"
echo ""
echo "📋 Access Points:"
echo "  🌐 Frontend: http://localhost:3000"
echo "  🔧 Backend API: http://localhost:8080/api"
echo ""
echo "🧪 Quick Test:"
echo "  curl http://localhost:8080/api/definitions"
echo ""
echo "🛑 To stop: docker-compose down"

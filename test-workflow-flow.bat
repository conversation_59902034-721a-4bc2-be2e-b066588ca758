@echo off
REM Test script to verify the complete BPMN workflow flow
echo 🧪 Testing BPMN Workflow System Flow
echo ====================================

REM Base URLs
set WORKFLOW_API=http://localhost:8080/api/v1/workflow
set FRONTEND_URL=http://localhost:3001

echo 🔍 Step 1: Testing API Connectivity
echo -----------------------------------

echo Testing: Get Process Definitions
curl -s "%WORKFLOW_API%/bpmn/definitions"
echo.

echo Testing: Get Active Process Instances
curl -s "%WORKFLOW_API%/bpmn/instances"
echo.

echo Testing: Get Historic Process Instances
curl -s "%WORKFLOW_API%/bpmn/instances/history"
echo.

echo Testing: Get Available Processes for Testing
curl -s "%WORKFLOW_API%/bpmn/test/available-processes"
echo.

echo 🚀 Step 2: Starting Test Processes
echo ----------------------------------

echo Starting HRDC Workflow Test Process...
curl -X POST "%WORKFLOW_API%/bpmn/test/start-process/Hrdc_workflow"
echo.

echo Starting Complaint Workflow Test Process...
curl -X POST "%WORKFLOW_API%/bpmn/test/start-process/Complaint_Workflow"
echo.

echo Starting Appeal Workflow Test Process...
curl -X POST "%WORKFLOW_API%/bpmn/test/start-process/Appeal_Workflow"
echo.

echo 📊 Step 3: Verifying Active Instances
echo ------------------------------------

echo Checking active instances after starting processes...
curl -s "%WORKFLOW_API%/bpmn/instances"
echo.

echo 🌐 Step 4: Testing Frontend Connectivity
echo ---------------------------------------

curl -s "%FRONTEND_URL%" >nul 2>&1
if errorlevel 1 (
    echo ❌ Frontend is not accessible at %FRONTEND_URL%
) else (
    echo ✅ Frontend is accessible at %FRONTEND_URL%
)
echo.

echo 🎯 Step 5: Manual Testing Instructions
echo ------------------------------------
echo 1. Open your browser and go to: %FRONTEND_URL%
echo 2. You should see the Workflow Management Dashboard
echo 3. Check the stats cards for:
echo    - Process Definitions count
echo    - Active Instances count (should be ^> 0 after running tests)
echo 4. Click on 'Active Instances' tab to see running processes
echo 5. Click 'View BPMN' on any process definition
echo 6. Select an active instance from the sidebar
echo 7. Verify that active tasks are highlighted in blue with pulse animation
echo.

echo 🧪 Step 6: Additional Test Commands
echo ---------------------------------
echo # Test starting a specific workflow:
echo curl -X POST "%WORKFLOW_API%/bpmn/test/start-process/Hrdc_workflow"
echo.
echo # Get status of a specific process instance:
echo curl "%WORKFLOW_API%/bpmn/instance/{INSTANCE_ID}/status"
echo.
echo # Start HRDC workflow via original endpoint:
echo curl "%WORKFLOW_API%/start-process/HRDC/TEST123"
echo.

echo 📋 Step 7: Troubleshooting
echo -------------------------
echo If you don't see active instances:
echo 1. Check Docker containers are running: docker-compose ps
echo 2. Check logs: docker-compose logs workflow-integration
echo 3. Verify database connection: docker-compose logs postgres-workflow
echo 4. Check frontend logs: docker-compose logs workflow-bpmn-viewer
echo.
echo If BPMN diagrams don't load:
echo 1. Check browser console for errors
echo 2. Verify API endpoints are accessible
echo 3. Check CORS configuration
echo.

echo 🎉 Test completed!
echo Open %FRONTEND_URL% to see your BPMN workflow system in action!
pause

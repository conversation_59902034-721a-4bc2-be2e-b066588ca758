#!/bin/bash

# Workflow System Startup Script
# This script starts the complete BPMN workflow system with Docker

echo "🚀 Starting BPMN Workflow System..."
echo "=================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

echo "✅ Docker is running"

# Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose down --remove-orphans

# Remove old volumes if requested
if [ "$1" = "--clean" ]; then
    echo "🗑️  Removing old volumes..."
    docker volume rm postgres_workflow_data 2>/dev/null || true
fi

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose up --build -d

# Wait for services to be healthy
echo "⏳ Waiting for services to start..."

# Function to check if a service is healthy
check_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1

    echo "Checking $service_name on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:$port/actuator/health > /dev/null 2>&1; then
            echo "✅ $service_name is healthy"
            return 0
        fi
        
        echo "⏳ Attempt $attempt/$max_attempts - $service_name not ready yet..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to start within expected time"
    return 1
}

# Check database
echo "📊 Checking PostgreSQL database..."
sleep 10

# Check workflow-integration service
if check_service "Workflow Integration" 8080; then
    echo "✅ Workflow Integration service is running"
else
    echo "❌ Workflow Integration service failed to start"
    docker-compose logs workflow-integration
    exit 1
fi

# Check workplace-learning service
if check_service "Workplace Learning" 8091; then
    echo "✅ Workplace Learning service is running"
else
    echo "❌ Workplace Learning service failed to start"
    docker-compose logs workplace-learning
    exit 1
fi

# Check frontend
echo "🌐 Checking BPMN Viewer frontend..."
if curl -f http://localhost:3001 > /dev/null 2>&1; then
    echo "✅ BPMN Viewer frontend is running"
else
    echo "❌ BPMN Viewer frontend failed to start"
    docker-compose logs workflow-bpmn-viewer
    exit 1
fi

echo ""
echo "🎉 BPMN Workflow System is now running!"
echo "======================================"
echo ""
echo "📋 Service URLs:"
echo "  🌐 BPMN Viewer Frontend:    http://localhost:3001"
echo "  🔧 Workflow Integration:    http://localhost:8080"
echo "  🏢 Workplace Learning:      http://localhost:8091"
echo "  🗄️  PostgreSQL Database:     localhost:5432"
echo ""
echo "📊 API Endpoints:"
echo "  📋 Process Definitions:     http://localhost:8080/api/v1/workflow/bpmn/definitions"
echo "  🔄 Process Instances:       http://localhost:8080/api/v1/workflow/bpmn/instances"
echo "  📈 Instance History:        http://localhost:8080/api/v1/workflow/bpmn/instances/history"
echo ""
echo "🧪 Test Commands:"
echo "  # Test API connectivity"
echo "  curl http://localhost:8080/api/v1/workflow/bpmn/definitions"
echo ""
echo "  # Start a workflow process"
echo "  curl http://localhost:8080/api/v1/workflow/start-process/HRDC/APP123"
echo ""
echo "📝 To view logs:"
echo "  docker-compose logs -f [service-name]"
echo ""
echo "🛑 To stop the system:"
echo "  docker-compose down"
echo ""
echo "🔍 Open your browser and navigate to http://localhost:3001 to view BPMN diagrams!"

-- Initialize workflow database
-- This script runs when PostgreSQL container starts for the first time

-- Create workflow database if it doesn't exist
-- (This is handled by POSTGRES_DB environment variable)

-- Create additional schemas if needed
-- CREATE SCHEMA IF NOT EXISTS flowable;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE workflow TO postgres;

-- Create extensions if needed
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Log initialization
SELECT 'Workflow database initialized successfully' AS status;

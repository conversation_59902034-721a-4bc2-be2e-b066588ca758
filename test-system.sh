#!/bin/bash

echo "🧪 Testing BPMN Workflow System"
echo "==============================="

# Test URLs
BACKEND_API="http://localhost:8080/api"
FRONTEND_URL="http://localhost:3000"

echo "📋 Step 1: Testing Backend API"
echo "------------------------------"

# Test process definitions
echo "Testing: GET $BACKEND_API/definitions"
curl -s "$BACKEND_API/definitions" | jq . || echo "❌ Failed"
echo ""

# Test BPMN XML retrieval
echo "Testing: GET $BACKEND_API/definition/check-demo/xml"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$BACKEND_API/definition/check-demo/xml")
http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
if [ "$http_code" -eq 200 ]; then
    echo "✅ BPMN XML retrieved successfully"
else
    echo "❌ Failed to retrieve BPMN XML (HTTP $http_code)"
fi
echo ""

echo "🚀 Step 2: Starting Test Process"
echo "--------------------------------"

# Start a test process
echo "Starting HRDC workflow..."
curl -X POST -s "$BACKEND_API/test/start-process/Hrdc_workflow" | jq . || echo "❌ Failed"
echo ""

echo "📊 Step 3: Checking Active Instances"
echo "-----------------------------------"

# Check active instances
echo "Getting active instances..."
curl -s "$BACKEND_API/instances" | jq . || echo "❌ Failed"
echo ""

echo "🌐 Step 4: Testing Frontend"
echo "---------------------------"

# Test frontend accessibility
if curl -s "$FRONTEND_URL" > /dev/null; then
    echo "✅ Frontend is accessible at $FRONTEND_URL"
else
    echo "❌ Frontend is not accessible"
fi
echo ""

echo "🎯 Step 5: Manual Testing Guide"
echo "------------------------------"
echo "1. Open browser: $FRONTEND_URL"
echo "2. You should see:"
echo "   - List of available processes"
echo "   - Process definition cards"
echo "   - Test buttons to start processes"
echo ""
echo "3. Click 'View BPMN' on any process to:"
echo "   - See interactive BPMN diagram"
echo "   - View process instances (if any)"
echo "   - See active task highlighting"
echo ""
echo "4. Use test buttons to start processes and see them in action"
echo ""

echo "✅ System test completed!"
echo "Open $FRONTEND_URL to interact with your BPMN workflow system."

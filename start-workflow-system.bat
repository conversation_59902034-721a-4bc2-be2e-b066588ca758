@echo off
REM Workflow System Startup Script for Windows
REM This script starts the complete BPMN workflow system with Docker

echo 🚀 Starting BPMN Workflow System...
echo ==================================

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker first.
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose is not installed. Please install docker-compose first.
    exit /b 1
)

echo ✅ Docker is running

REM Clean up any existing containers
echo 🧹 Cleaning up existing containers...
docker-compose down --remove-orphans

REM Remove old volumes if requested
if "%1"=="--clean" (
    echo 🗑️  Removing old volumes...
    docker volume rm postgres_workflow_data 2>nul
)

REM Build and start services
echo 🏗️  Building and starting services...
docker-compose up --build -d

REM Wait for services to be healthy
echo ⏳ Waiting for services to start...
timeout /t 30 /nobreak >nul

REM Check workflow-integration service
echo 📊 Checking Workflow Integration service...
curl -f http://localhost:8080/actuator/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Workflow Integration service failed to start
    docker-compose logs workflow-integration
    exit /b 1
) else (
    echo ✅ Workflow Integration service is running
)

REM Check workplace-learning service
echo 🏢 Checking Workplace Learning service...
curl -f http://localhost:8091/actuator/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Workplace Learning service failed to start
    docker-compose logs workplace-learning
    exit /b 1
) else (
    echo ✅ Workplace Learning service is running
)

REM Check frontend
echo 🌐 Checking BPMN Viewer frontend...
curl -f http://localhost:3001 >nul 2>&1
if errorlevel 1 (
    echo ❌ BPMN Viewer frontend failed to start
    docker-compose logs workflow-bpmn-viewer
    exit /b 1
) else (
    echo ✅ BPMN Viewer frontend is running
)

echo.
echo 🎉 BPMN Workflow System is now running!
echo ======================================
echo.
echo 📋 Service URLs:
echo   🌐 BPMN Viewer Frontend:    http://localhost:3001
echo   🔧 Workflow Integration:    http://localhost:8080
echo   🏢 Workplace Learning:      http://localhost:8091
echo   🗄️  PostgreSQL Database:     localhost:5432
echo.
echo 📊 API Endpoints:
echo   📋 Process Definitions:     http://localhost:8080/api/v1/workflow/bpmn/definitions
echo   🔄 Process Instances:       http://localhost:8080/api/v1/workflow/bpmn/instances
echo   📈 Instance History:        http://localhost:8080/api/v1/workflow/bpmn/instances/history
echo.
echo 🧪 Test Commands:
echo   REM Test API connectivity
echo   curl http://localhost:8080/api/v1/workflow/bpmn/definitions
echo.
echo   REM Start a workflow process
echo   curl http://localhost:8080/api/v1/workflow/start-process/HRDC/APP123
echo.
echo 📝 To view logs:
echo   docker-compose logs -f [service-name]
echo.
echo 🛑 To stop the system:
echo   docker-compose down
echo.
echo 🔍 Open your browser and navigate to http://localhost:3001 to view BPMN diagrams!
pause
